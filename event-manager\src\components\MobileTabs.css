/* Mobile Tabs Styling */
.mobile-tabs {
  display: flex;
  overflow-x: auto;
  background-color: var(--dark-surface);
  border-radius: 8px;
  margin: 1rem 0;
  padding: 0.5rem;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
}

.mobile-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.mobile-tab {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: 0.7rem 1.2rem;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.mobile-tab.active {
  background-color: rgba(var(--primary-rgb), 0.15);
  color: var(--primary);
}

.mobile-tab:hover:not(.active) {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

/* Mobile Tab Content Styling */
.mobile-tab-contents {
  margin-top: 1.5rem;
  width: 100%;
  overflow: hidden;
}

.mobile-tab-content {
  display: none;
  animation: fadeIn 0.5s ease;
  width: 100%;
}

.mobile-tab-content.active {
  display: block;
}

/* Tab content specific styles */
.tab-content-about,
.tab-content-schedule,
.tab-content-register,
.tab-content-details,
.tab-content-events,
.tab-content-team,
.tab-content-gallery,
.tab-content-contact,
.tab-content-nit,
.tab-content-website,
.tab-content-creator {
  padding: 0.5rem;
  width: 100%;
}

/* Social share buttons in mobile view */
.social-share-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1.5rem;
  position: relative;
  justify-content: center;
}

.social-share-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.social-share-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .mobile-tab {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }
}
