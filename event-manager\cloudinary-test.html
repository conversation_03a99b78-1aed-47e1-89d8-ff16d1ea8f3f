<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudinary Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #3f51b5;
            text-align: center;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .upload-area {
            border: 2px dashed #3f51b5;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            border-radius: 8px;
            cursor: pointer;
        }
        .button {
            background-color: #3f51b5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        .button:disabled {
            background-color: #9e9e9e;
            cursor: not-allowed;
        }
        .progress {
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }
        .progress-bar {
            height: 100%;
            background-color: #3f51b5;
            width: 0%;
            transition: width 0.3s;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 4px;
            display: none;
        }
        .error {
            color: #f44336;
            margin-top: 10px;
            padding: 10px;
            background-color: rgba(244, 67, 54, 0.1);
            border-radius: 4px;
            display: none;
        }
        img {
            max-width: 100%;
            margin-top: 10px;
            border-radius: 4px;
        }
        .config {
            background-color: #e8eaf6;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Cloudinary Upload Test</h1>

    <div class="container">
        <div class="config">
            <h3>Configuration</h3>
            <p><strong>Cloud Name:</strong> dmsvblrzv</p>
            <p><strong>Upload Preset:</strong> nits_preset</p>
        </div>

        <input type="file" id="fileInput" style="display: none;" accept="image/*">
        <div class="upload-area" id="uploadArea">
            <h3>Click to select an image</h3>
            <p id="fileName">No file selected</p>
        </div>

        <button id="uploadButton" class="button" disabled>Upload Image</button>

        <div class="progress" id="progressContainer" style="display: none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div class="error" id="errorMessage"></div>

        <div class="result" id="resultContainer">
            <h3>Upload Result</h3>
            <p><strong>URL:</strong> <span id="imageUrl"></span></p>
            <p><strong>Public ID:</strong> <span id="publicId"></span></p>
            <p><strong>Size:</strong> <span id="imageSize"></span></p>
            <p><strong>Format:</strong> <span id="imageFormat"></span></p>
            <div>
                <h4>Preview:</h4>
                <img id="imagePreview" src="" alt="Uploaded image">
            </div>
        </div>
    </div>

    <script>
        // Cloudinary configuration
        const cloudName = 'dmsvblrzv';
        const uploadPreset = 'nits_preset';

        // DOM elements
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const fileName = document.getElementById('fileName');
        const uploadButton = document.getElementById('uploadButton');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const errorMessage = document.getElementById('errorMessage');
        const resultContainer = document.getElementById('resultContainer');
        const imageUrl = document.getElementById('imageUrl');
        const publicId = document.getElementById('publicId');
        const imageSize = document.getElementById('imageSize');
        const imageFormat = document.getElementById('imageFormat');
        const imagePreview = document.getElementById('imagePreview');

        // Event listeners
        uploadArea.addEventListener('click', () => fileInput.click());

        fileInput.addEventListener('change', (e) => {
            if (e.target.files && e.target.files[0]) {
                const file = e.target.files[0];
                fileName.textContent = file.name;
                uploadButton.disabled = false;

                // Reset previous results
                errorMessage.style.display = 'none';
                resultContainer.style.display = 'none';
            }
        });

        uploadButton.addEventListener('click', uploadImage);

        // Upload function
        function uploadImage() {
            const file = fileInput.files[0];
            if (!file) return;

            // Reset UI
            errorMessage.style.display = 'none';
            resultContainer.style.display = 'none';
            uploadButton.disabled = true;
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';

            // Create FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('upload_preset', uploadPreset);
            formData.append('folder', 'test-uploads');

            // Create XMLHttpRequest
            const xhr = new XMLHttpRequest();

            xhr.open('POST', `https://api.cloudinary.com/v1_1/${cloudName}/upload`);

            xhr.onload = function() {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    displayResult(response);
                } else {
                    showError(`Upload failed with status: ${xhr.status}`);
                    console.error('Response:', xhr.responseText);
                }
                uploadButton.disabled = false;
            };

            xhr.onerror = function() {
                showError('Network error during upload');
                uploadButton.disabled = false;
            };

            xhr.upload.onprogress = function(e) {
                if (e.lengthComputable) {
                    const percent = Math.round((e.loaded / e.total) * 100);
                    progressBar.style.width = `${percent}%`;
                }
            };

            xhr.send(formData);
        }

        // Display result
        function displayResult(data) {
            imageUrl.textContent = data.secure_url;
            publicId.textContent = data.public_id;
            imageSize.textContent = `${data.width} x ${data.height}`;
            imageFormat.textContent = data.format;
            imagePreview.src = data.secure_url;

            resultContainer.style.display = 'block';
            progressContainer.style.display = 'none';
        }

        // Show error
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            progressContainer.style.display = 'none';
        }
    </script>
</body>
</html>
