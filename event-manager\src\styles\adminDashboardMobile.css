/* Admin Dashboard Mobile Styles */

/* Ensure admin dashboard takes full height when navbar is hidden */
.admin-dashboard {
  min-height: 100vh;
  padding-top: 0 !important; /* Remove top padding since navbar is hidden */
}

/* Desktop styles for admin dashboard */
@media (min-width: 769px) {
  .admin-dashboard {
    padding: 2rem 0 !important;
  }

  .admin-dashboard .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  /* Ensure proper spacing on desktop */
  .admin-dashboard-header {
    margin-bottom: 2rem;
  }

  .admin-dashboard-tabs {
    margin-bottom: 2rem;
  }

  /* Create Club Preview on Desktop */
  .create-club-preview {
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
  }

  .create-club-preview:hover {
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Desktop scrolling fixes for detail views and editors */
  .admin-club-details,
  .admin-event-details,
  .club-editor.full-page,
  .event-editor.full-page {
    position: relative !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scroll-behavior: smooth !important;
    height: auto !important;
    max-height: 90vh !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
  }

  /* Enable direct scrolling on desktop */
  .admin-club-details,
  .admin-event-details,
  .club-editor.full-page,
  .event-editor.full-page,
  .admin-club-details *,
  .admin-event-details *,
  .club-editor.full-page *,
  .event-editor.full-page * {
    scroll-behavior: smooth !important;
  }

  /* Remove mobile-specific positioning on desktop */
  body.modal-open {
    overflow: auto !important;
    position: static !important;
    width: auto !important;
  }
}

/* General mobile adjustments for admin dashboard */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 1rem 0 !important;
  }

  .admin-dashboard .container {
    padding: 0 1rem;
  }

  /* Dashboard Header */
  .admin-dashboard-header {
    flex-direction: column !important;
    align-items: flex-start !important;
    padding: 1.25rem !important;
    gap: 1rem;
  }

  .admin-dashboard-header > div:first-child {
    width: 100%;
    text-align: center;
    margin-bottom: 1rem;
  }

  .admin-dashboard-header h2 {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .admin-dashboard-header p {
    font-size: 0.9rem !important;
  }

  .admin-dashboard-header button {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
  }

  /* Success and Error Messages */
  .admin-dashboard .container > div[style*="backgroundColor: rgba(46, 204, 113, 0.1)"],
  .admin-dashboard .container > div[style*="backgroundColor: rgba(255, 0, 0, 0.1)"] {
    padding: 0.75rem !important;
    margin: 0 -1rem 1rem -1rem !important;
    border-radius: 0 !important;
    font-size: 0.9rem !important;
  }

  /* Dashboard Tabs */
  .admin-dashboard-tabs {
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem !important;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .admin-dashboard-tabs::-webkit-scrollbar {
    display: none;
  }

  .admin-dashboard .tab-button {
    padding: 0.75rem 1rem !important;
    font-size: 0.85rem !important;
    flex-shrink: 0;
    min-width: auto;
    white-space: nowrap;
  }

  /* Club Requests Tab */
  .requests-tab h3 {
    font-size: 1.2rem !important;
    margin-bottom: 1rem !important;
  }

  .requests-list > div {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
  }

  /* Request card layout adjustments */
  .requests-list > div > div:first-child {
    flex-direction: column !important;
    align-items: flex-start !important;
  }

  .requests-list > div > div:first-child > div:first-child {
    flex-direction: column !important;
    align-items: center !important;
    width: 100% !important;
    margin-bottom: 1rem !important;
  }

  .requests-list > div > div:first-child > div:first-child > div:first-child {
    margin-right: 0 !important;
    margin-bottom: 0.75rem !important;
  }

  .requests-list > div > div:first-child > div:first-child > div:first-child img {
    width: 80px !important;
    height: 80px !important;
  }

  .requests-list > div > div:first-child > div:first-child > div:last-child {
    text-align: center;
  }

  .requests-list > div > div:first-child > span {
    align-self: flex-start !important;
    margin-top: 0.5rem !important;
  }

  /* Request action buttons */
  .requests-list > div > div:last-child {
    flex-direction: column !important;
    gap: 0.75rem !important;
    margin-top: 1rem !important;
  }

  .requests-list > div > div:last-child button {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
  }

  /* Create Club Tab */
  .create-club-tab h3 {
    font-size: 1.2rem !important;
    margin-bottom: 1rem !important;
  }

  /* Create Club Preview Card */
  .create-club-preview {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: border-color 0.3s ease;
  }

  .create-club-preview:hover {
    border-color: rgba(255, 255, 255, 0.2);
  }

  .create-club-preview > div:first-child {
    flex-direction: column !important;
    align-items: flex-start !important;
  }

  .create-club-preview > div:first-child > div:first-child {
    flex-direction: column !important;
    align-items: center !important;
    width: 100% !important;
    margin-bottom: 1rem !important;
  }

  .create-club-preview > div:first-child > div:first-child > div:first-child {
    margin-right: 0 !important;
    margin-bottom: 0.75rem !important;
  }

  .create-club-preview > div:first-child > div:first-child > div:first-child > div {
    width: 80px !important;
    height: 80px !important;
  }

  .create-club-preview > div:first-child > div:first-child > div:last-child {
    text-align: center;
  }

  .create-club-preview > div:first-child > span {
    align-self: flex-start !important;
    margin-top: 0.5rem !important;
  }

  .create-club-tab form {
    padding: 1rem !important;
  }

  .create-club-tab h4 {
    font-size: 1rem !important;
    margin-bottom: 0.75rem !important;
  }

  /* Form layout adjustments */
  .create-club-tab form > div > div[style*="display: flex"] {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .create-club-tab form > div > div[style*="display: flex"] > div {
    flex: none !important;
  }

  .create-club-tab form input,
  .create-club-tab form textarea {
    font-size: 16px !important; /* Prevents iOS zoom on focus */
    padding: 0.75rem 1rem !important;
  }

  .create-club-tab form label {
    font-size: 0.9rem !important;
    margin-bottom: 0.5rem !important;
  }

  /* Form action buttons */
  .create-club-tab form > div:last-child {
    flex-direction: column !important;
    gap: 0.75rem !important;
    margin-top: 1rem !important;
  }

  .create-club-tab form > div:last-child button {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
  }

  /* Clubs List Grid */
  .clubs-list {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .clubs-list > div {
    padding: 1rem !important;
  }

  .clubs-list > div > div:first-child {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center;
    margin-bottom: 1rem !important;
  }

  .clubs-list > div > div:first-child > div:first-child {
    margin-right: 0 !important;
    margin-bottom: 0.75rem !important;
  }

  .clubs-list > div > div:last-child {
    justify-content: center !important;
  }

  .clubs-list > div > div:last-child button {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
  }

  /* Events List */
  .events-list > div {
    flex-direction: column !important;
    gap: 1rem !important;
    padding: 1rem !important;
  }

  .events-list > div > div:first-child {
    width: 100% !important;
    height: 200px !important;
    flex-shrink: 1 !important;
  }

  .events-list > div > div:last-child > div:first-child {
    flex-direction: column !important;
    align-items: flex-start !important;
  }

  .events-list > div > div:last-child > div:first-child > div:last-child {
    margin-top: 0.75rem !important;
    align-self: flex-start !important;
  }

  .events-list > div > div:last-child > div:last-child {
    flex-direction: column !important;
    gap: 0.75rem !important;
    margin-top: 1rem !important;
  }

  .events-list > div > div:last-child > div:last-child button {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
  }

  /* Modal adjustments */
  .admin-dashboard div[style*="position: fixed"][style*="zIndex: 1000"] {
    padding: 1rem !important;
  }

  .admin-dashboard div[style*="position: fixed"][style*="zIndex: 1000"] > div {
    width: 100% !important;
    max-width: none !important;
    max-height: 95vh !important;
  }

  /* Approval Modal */
  .admin-dashboard div[style*="position: fixed"] div[style*="backgroundColor: var(--dark-surface)"] {
    padding: 1rem !important;
    max-width: none !important;
    width: 100% !important;
  }

  .admin-dashboard div[style*="position: fixed"] div[style*="backgroundColor: var(--dark-surface)"] h3 {
    font-size: 1.1rem !important;
    margin-bottom: 1rem !important;
  }

  .admin-dashboard div[style*="position: fixed"] div[style*="backgroundColor: var(--dark-surface)"] > div:nth-child(2) {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center;
  }

  .admin-dashboard div[style*="position: fixed"] div[style*="backgroundColor: var(--dark-surface)"] > div:nth-child(2) > div:first-child {
    margin-right: 0 !important;
    margin-bottom: 1rem !important;
  }

  .admin-dashboard div[style*="position: fixed"] div[style*="backgroundColor: var(--dark-surface)"] > div:nth-child(2) img {
    width: 100px !important;
    height: 100px !important;
  }

  .admin-dashboard div[style*="position: fixed"] div[style*="backgroundColor: var(--dark-surface)"] ul {
    margin: 1rem 0;
    padding-left: 1rem;
  }

  .admin-dashboard div[style*="position: fixed"] div[style*="backgroundColor: var(--dark-surface)"] ul li {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
  }

  .admin-dashboard div[style*="position: fixed"] div[style*="backgroundColor: var(--dark-surface)"] > div:last-child {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .admin-dashboard div[style*="position: fixed"] div[style*="backgroundColor: var(--dark-surface)"] > div:last-child button {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .admin-dashboard {
    padding: 0.5rem 0 !important;
  }

  .admin-dashboard .container {
    padding: 0 0.75rem;
  }

  .admin-dashboard-header {
    padding: 1rem !important;
  }

  .admin-dashboard-header h2 {
    font-size: 1.3rem !important;
  }

  .admin-dashboard .tab-button {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.8rem !important;
  }

  .requests-list > div,
  .clubs-list > div,
  .events-list > div {
    padding: 0.75rem !important;
  }

  .create-club-tab form {
    padding: 0.75rem !important;
  }

  .create-club-tab form input,
  .create-club-tab form textarea {
    padding: 0.6rem 0.8rem !important;
  }

  /* Detail views for small mobile */
  div[style*="position: fixed"][style*="backgroundColor: rgba(0, 0, 0, 0.8)"] {
    padding: 0.25rem !important;
  }

  div[style*="width: 90%"][style*="maxWidth: 1200px"] {
    width: 98% !important;
    max-height: 98vh !important;
  }

  /* Detail view headers for small mobile */
  .admin-club-details > div[style*="padding: 1.5rem"][style*="backgroundColor: var(--dark-surface)"],
  .admin-event-details > div[style*="padding: 1.5rem"][style*="backgroundColor: var(--dark-surface)"] {
    padding: 0.75rem !important;
  }

  /* Detail view content for small mobile */
  .admin-club-details div[style*="padding: 1.5rem"]:not([style*="backgroundColor: var(--dark-surface)"]),
  .admin-event-details div[style*="padding: 1.5rem"]:not([style*="backgroundColor: var(--dark-surface)"]) {
    padding: 0.75rem !important;
  }

  /* Information cards for small mobile */
  .admin-club-details div[style*="backgroundColor: var(--dark-surface)"][style*="padding: 1.5rem"],
  .admin-event-details div[style*="backgroundColor: var(--dark-surface)"][style*="padding: 1.5rem"] {
    padding: 0.75rem !important;
  }

  /* Tab buttons for small mobile */
  .admin-club-details div[style*="display: flex"][style*="borderBottom: 1px solid"] button,
  .admin-event-details div[style*="display: flex"][style*="borderBottom: 1px solid"] button {
    min-width: 100px !important;
    padding: 0.6rem 0.8rem !important;
    font-size: 0.85rem !important;
  }

  /* Export buttons for small mobile */
  .registrations-tab .export-buttons button {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.85rem !important;
  }

  /* Table cells for small mobile */
  .registrations-tab th,
  .registrations-tab td {
    padding: 0.5rem 0.3rem !important;
    font-size: 0.8rem !important;
  }

  .registrations-tab th {
    font-size: 0.75rem !important;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .admin-dashboard .tab-button {
    padding: 0.5rem 0.6rem !important;
    font-size: 0.75rem !important;
  }

  .requests-list > div > div:last-child button,
  .clubs-list > div > div:last-child button,
  .events-list > div > div:last-child button {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.85rem !important;
  }
}

/* Additional mobile enhancements */
@media (max-width: 768px) {
  /* Improve touch targets */
  .admin-dashboard button {
    min-height: 44px;
    touch-action: manipulation;
  }

  /* Better spacing for readability */
  .admin-dashboard h3 {
    line-height: 1.3;
    margin-bottom: 1rem;
  }

  .admin-dashboard h4 {
    line-height: 1.3;
    margin-bottom: 0.75rem;
  }

  .admin-dashboard p {
    line-height: 1.5;
  }

  /* Improve form accessibility */
  .admin-dashboard input:focus,
  .admin-dashboard textarea:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }

  /* Status badges mobile optimization */
  .admin-dashboard span[style*="padding: 0.3rem 0.8rem"] {
    padding: 0.4rem 0.8rem !important;
    font-size: 0.8rem !important;
    white-space: nowrap;
  }

  /* Loading states */
  .admin-dashboard div[style*="textAlign: center"][style*="padding: 2rem"] {
    padding: 1.5rem 1rem !important;
    font-size: 0.9rem;
  }

  /* Empty states */
  .admin-dashboard div[style*="textAlign: center"][style*="backgroundColor: var(--dark-surface)"] {
    padding: 1.5rem 1rem !important;
    margin: 0 -1rem !important;
    border-radius: 0 !important;
  }

  /* Scroll indicators for tabs */
  .admin-dashboard-tabs::after {
    content: "← Scroll for more tabs →";
    position: absolute;
    bottom: -1.5rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    color: var(--text-secondary);
    opacity: 0.7;
    white-space: nowrap;
    pointer-events: none;
  }

  /* Hide scroll indicator when not needed */
  @media (min-width: 640px) {
    .admin-dashboard-tabs::after {
      display: none;
    }
  }

  /* Improve image handling */
  .admin-dashboard img {
    max-width: 100%;
    height: auto;
  }

  /* Better button spacing in action groups */
  .admin-dashboard div[style*="display: flex"][style*="gap: 0.5rem"] {
    gap: 0.75rem !important;
  }

  /* Improve modal backdrop */
  .admin-dashboard div[style*="position: fixed"][style*="backgroundColor: rgba(0, 0, 0, 0.8)"] {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }

  /* Better text truncation */
  .admin-dashboard p[style*="color: var(--text-secondary)"] {
    word-break: break-word;
    hyphens: auto;
  }

  /* Improve list item spacing */
  .requests-list > div + div,
  .events-list > div + div {
    margin-top: 1rem !important;
  }

  /* Better form field spacing */
  .create-club-tab form > div {
    margin-bottom: 1.25rem !important;
  }

  .create-club-tab form > div:last-child {
    margin-bottom: 0 !important;
  }

  /* Admin Detail Views Mobile Optimization */

  /* Detail view modal containers - target the modal wrapper */
  div[style*="position: fixed"][style*="backgroundColor: rgba(0, 0, 0, 0.8)"] {
    padding: 0.5rem !important;
  }

  /* Detail view modal content - target the inner container */
  div[style*="width: 90%"][style*="maxWidth: 1200px"] {
    width: 95% !important;
    max-width: none !important;
    max-height: 95vh !important;
  }

  /* Complete Mobile Redesign for Admin Detail Views */

  /* Headers - Improved mobile layout */
  .admin-club-details > div:first-child,
  .admin-event-details > div:first-child {
    padding: 1.5rem 1rem !important;
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 1.5rem !important;
    background: var(--dark-surface) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  /* Header club/event info section - Better mobile layout */
  .admin-club-details > div:first-child > div:first-child,
  .admin-event-details > div:first-child > div:first-child {
    width: 100% !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    gap: 1rem !important;
    margin-bottom: 0 !important;
    padding: 1rem !important;
    background: rgba(255, 255, 255, 0.03) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
  }

  /* Logo/avatar container - Enhanced styling */
  .admin-club-details > div:first-child > div:first-child > div:first-child,
  .admin-event-details > div:first-child > div:first-child > div:first-child {
    margin-bottom: 0.75rem !important;
    padding: 0.5rem !important;
    background: rgba(var(--primary-rgb), 0.1) !important;
    border-radius: 50% !important;
    border: 2px solid rgba(var(--primary-rgb), 0.2) !important;
  }

  /* Club/Event title styling */
  .admin-club-details > div:first-child > div:first-child h2,
  .admin-event-details > div:first-child > div:first-child h2 {
    font-size: 1.5rem !important;
    margin: 0.5rem 0 !important;
    color: var(--text-primary) !important;
    font-weight: 600 !important;
  }

  /* Email/subtitle styling */
  .admin-club-details > div:first-child > div:first-child p,
  .admin-event-details > div:first-child > div:first-child p {
    font-size: 0.95rem !important;
    color: var(--text-secondary) !important;
    margin: 0 !important;
    opacity: 0.8 !important;
  }

  /* Header action buttons - Improved layout */
  .admin-club-details > div:first-child > div:last-child,
  .admin-event-details > div:first-child > div:last-child {
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
    align-items: stretch !important;
  }

  /* Make buttons full width with better styling */
  .admin-club-details > div:first-child > div:last-child button,
  .admin-event-details > div:first-child > div:last-child button {
    width: 100% !important;
    padding: 0.875rem 1rem !important;
    font-size: 0.95rem !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
  }

  /* Primary button styling */
  .admin-club-details > div:first-child > div:last-child button[style*="backgroundColor: var(--primary)"],
  .admin-event-details > div:first-child > div:last-child button[style*="backgroundColor: var(--primary)"] {
    background: linear-gradient(135deg, var(--primary), rgba(var(--primary-rgb), 0.8)) !important;
    box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3) !important;
  }

  /* Secondary button styling */
  .admin-club-details > div:first-child > div:last-child button[style*="backgroundColor: transparent"],
  .admin-event-details > div:first-child > div:last-child button[style*="backgroundColor: transparent"] {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(10px) !important;
  }

  /* Detail view tab navigation - Enhanced mobile design */
  .admin-club-details div[style*="display: flex"][style*="borderBottom: 1px solid"],
  .admin-event-details div[style*="display: flex"][style*="borderBottom: 1px solid"] {
    padding: 0 1rem !important;
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
    background: var(--dark-surface) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  .admin-club-details div[style*="display: flex"][style*="borderBottom: 1px solid"]::-webkit-scrollbar,
  .admin-event-details div[style*="display: flex"][style*="borderBottom: 1px solid"]::-webkit-scrollbar {
    display: none !important;
  }

  /* Tab buttons - Modern mobile design */
  .admin-club-details div[style*="display: flex"][style*="borderBottom: 1px solid"] button,
  .admin-event-details div[style*="display: flex"][style*="borderBottom: 1px solid"] button {
    min-width: 120px !important;
    white-space: nowrap !important;
    padding: 1rem 1.5rem !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    flex-shrink: 0 !important;
    border-radius: 8px 8px 0 0 !important;
    border-bottom: 3px solid transparent !important;
    transition: all 0.3s ease !important;
    background: transparent !important;
    color: var(--text-secondary) !important;
    position: relative !important;
  }

  /* Tab button hover effect */
  .admin-club-details div[style*="display: flex"][style*="borderBottom: 1px solid"] button:hover,
  .admin-event-details div[style*="display: flex"][style*="borderBottom: 1px solid"] button:hover {
    background: rgba(255, 255, 255, 0.05) !important;
    color: var(--text-primary) !important;
  }

  /* Active tab styling - Enhanced */
  .admin-club-details div[style*="display: flex"][style*="borderBottom: 1px solid"] button[style*="borderBottom"],
  .admin-event-details div[style*="display: flex"][style*="borderBottom: 1px solid"] button[style*="borderBottom"] {
    color: var(--primary) !important;
    background: rgba(var(--primary-rgb), 0.1) !important;
    border-bottom: 3px solid var(--primary) !important;
    box-shadow: 0 -2px 8px rgba(var(--primary-rgb), 0.2) !important;
  }

  /* Detail view content padding - Enhanced */
  .admin-club-details div[style*="padding: 1.5rem"]:not([style*="backgroundColor: var(--dark-surface)"]),
  .admin-event-details div[style*="padding: 1.5rem"]:not([style*="backgroundColor: var(--dark-surface)"]) {
    padding: 1.5rem 1rem !important;
    background: var(--dark-bg) !important;
  }

  /* Enhanced Information cards styling - Modern mobile design */
  .admin-club-details div[style*="backgroundColor: var(--dark-surface)"][style*="padding: 1.5rem"],
  .admin-event-details div[style*="backgroundColor: var(--dark-surface)"][style*="padding: 1.5rem"] {
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
    border-radius: 12px !important;
    border: 1px solid rgba(255, 255, 255, 0.08) !important;
    background: linear-gradient(135deg, var(--dark-surface), rgba(255, 255, 255, 0.02)) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(10px) !important;
  }

  /* Information card headers - Enhanced styling */
  .admin-club-details div[style*="backgroundColor: var(--dark-surface)"] h3,
  .admin-event-details div[style*="backgroundColor: var(--dark-surface)"] h3 {
    font-size: 1.15rem !important;
    margin-bottom: 1rem !important;
    color: var(--text-primary) !important;
    font-weight: 600 !important;
    padding-bottom: 0.5rem !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  /* Information card content - Better readability */
  .admin-club-details div[style*="backgroundColor: var(--dark-surface)"] p,
  .admin-event-details div[style*="backgroundColor: var(--dark-surface)"] p {
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
    word-break: break-word !important;
    color: var(--text-secondary) !important;
    margin-bottom: 0.75rem !important;
  }

  /* Information card labels and values */
  .admin-club-details div[style*="backgroundColor: var(--dark-surface)"] div[style*="display: flex"],
  .admin-event-details div[style*="backgroundColor: var(--dark-surface)"] div[style*="display: flex"] {
    flex-direction: column !important;
    gap: 0.5rem !important;
    margin-bottom: 1rem !important;
    padding: 0.75rem !important;
    background: rgba(255, 255, 255, 0.02) !important;
    border-radius: 8px !important;
    border-left: 3px solid var(--primary) !important;
  }

  /* Field labels - Enhanced styling */
  .admin-club-details div[style*="backgroundColor: var(--dark-surface)"] strong,
  .admin-event-details div[style*="backgroundColor: var(--dark-surface)"] strong {
    font-size: 0.85rem !important;
    color: var(--primary) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    font-weight: 600 !important;
    margin-bottom: 0.25rem !important;
    display: block !important;
  }

  /* Field values - Better formatting */
  .admin-club-details div[style*="backgroundColor: var(--dark-surface)"] span,
  .admin-event-details div[style*="backgroundColor: var(--dark-surface)"] span {
    font-size: 0.95rem !important;
    color: var(--text-primary) !important;
    font-weight: 400 !important;
    word-break: break-all !important;
  }

  /* Force ALL grids to single column on mobile - more specific targeting */
  .admin-club-details div[style*="display: grid"],
  .admin-event-details div[style*="display: grid"],
  .admin-club-details div[style*="gridTemplateColumns"],
  .admin-event-details div[style*="gridTemplateColumns"] {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    grid-template-columns: none !important;
  }

  /* Specifically target the main content grid */
  .admin-club-details > div:last-child > div[style*="display: grid"],
  .admin-event-details > div:last-child > div[style*="display: grid"] {
    display: flex !important;
    flex-direction: column !important;
    gap: 1.5rem !important;
  }

  /* Target nested grids in information cards */
  .admin-club-details div[style*="backgroundColor: var(--dark-surface)"] div[style*="display: grid"],
  .admin-event-details div[style*="backgroundColor: var(--dark-surface)"] div[style*="display: grid"] {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
  }

  /* Content sections - better mobile spacing */
  .admin-club-details > div:last-child,
  .admin-event-details > div:last-child {
    padding: 1rem !important;
  }

  /* Information cards - mobile optimized */
  .admin-club-details div[style*="backgroundColor: var(--dark-surface)"],
  .admin-event-details div[style*="backgroundColor: var(--dark-surface)"] {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
    border-radius: 8px !important;
  }

  /* Section headings */
  .admin-club-details h3,
  .admin-event-details h3 {
    font-size: 1.1rem !important;
    margin-bottom: 0.75rem !important;
    margin-top: 0 !important;
  }

  /* Field labels and values - better mobile formatting */
  .admin-club-details p[style*="color: var(--text-secondary)"][style*="fontSize: 0.8rem"],
  .admin-event-details p[style*="color: var(--text-secondary)"][style*="fontSize: 0.8rem"] {
    font-size: 0.75rem !important;
    margin-bottom: 0.25rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
  }

  .admin-club-details p[style*="fontSize: 0.95rem"],
  .admin-event-details p[style*="fontSize: 0.95rem"] {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
    word-break: break-word !important;
  }

  /* Events section - complete mobile redesign */
  .club-events-tab,
  .registrations-tab {
    padding: 0 !important;
  }

  /* Events header section */
  .club-events-tab > div:first-child,
  .registrations-tab > div:first-child {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.75rem !important;
    margin-bottom: 1rem !important;
    padding: 0 !important;
  }

  /* Aggressive override for grid layouts - target specific patterns */
  .admin-club-details div[style*="gridTemplateColumns: 1fr 1fr"],
  .admin-event-details div[style*="gridTemplateColumns: 1fr 1fr"],
  .admin-club-details div[style*="grid-template-columns: 1fr 1fr"],
  .admin-event-details div[style*="grid-template-columns: 1fr 1fr"] {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    grid-template-columns: 1fr !important;
  }

  /* Override auto-fill grids */
  .admin-club-details div[style*="gridTemplateColumns: repeat(auto-fill"],
  .admin-event-details div[style*="gridTemplateColumns: repeat(auto-fill"],
  .admin-club-details div[style*="grid-template-columns: repeat(auto-fill"],
  .admin-event-details div[style*="grid-template-columns: repeat(auto-fill"] {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    grid-template-columns: 1fr !important;
  }

  /* Event cards - mobile optimized */
  .club-events-tab div[style*="backgroundColor: var(--dark-surface)"][style*="borderRadius: 8px"] {
    margin-bottom: 1rem !important;
    border-radius: 8px !important;
    overflow: hidden !important;
  }

  /* Event card content */
  .club-events-tab div[style*="backgroundColor: var(--dark-surface)"] > div[style*="padding: 1rem"] {
    padding: 1rem !important;
  }

  /* Event card headers with badges */
  .club-events-tab div[style*="display: flex"][style*="justifyContent: space-between"][style*="marginBottom: 0.5rem"] {
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
    align-items: flex-start !important;
  }

  /* Event titles */
  .club-events-tab h4 {
    font-size: 1.1rem !important;
    line-height: 1.3 !important;
    margin-bottom: 0.5rem !important;
  }

  /* Event descriptions */
  .club-events-tab p[style*="WebkitLineClamp: 2"] {
    -webkit-line-clamp: 3 !important;
    font-size: 0.85rem !important;
    line-height: 1.4 !important;
  }

  /* Export buttons in event details */
  .registrations-tab .export-buttons {
    flex-direction: column !important;
    gap: 0.5rem !important;
    width: 100% !important;
  }

  .registrations-tab .export-buttons button {
    width: 100% !important;
    justify-content: center !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem !important;
  }

  /* Registration table wrapper */
  .registrations-tab div[style*="overflowX: auto"] {
    margin: 0 -1rem !important;
    border-radius: 0 !important;
  }

  .registrations-tab table {
    min-width: 800px !important;
  }

  .registrations-tab th,
  .registrations-tab td {
    padding: 0.75rem 0.5rem !important;
    font-size: 0.85rem !important;
  }

  /* Registration table headers */
  .registrations-tab th {
    font-size: 0.8rem !important;
    white-space: nowrap !important;
  }

  /* Team members view button */
  .registrations-tab button[style*="backgroundColor: rgba(var(--primary-rgb), 0.1)"] {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.75rem !important;
  }

  /* Status badges in tables */
  .registrations-tab span[style*="display: inline-block"][style*="padding: 0.25rem 0.5rem"] {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.75rem !important;
  }

  /* Organizer section in event details */
  .event-details-tab div[style*="cursor: pointer"][style*="transition: background-color"] {
    padding: 1rem !important;
  }

  .event-details-tab div[style*="cursor: pointer"] div[style*="display: flex"][style*="alignItems: center"] {
    flex-direction: column !important;
    text-align: center !important;
    gap: 0.75rem !important;
  }

  .event-details-tab div[style*="cursor: pointer"] div[style*="width: 50px"] {
    width: 60px !important;
    height: 60px !important;
  }

  /* Registration tab header with export buttons */
  .registrations-tab div[style*="display: flex"][style*="justify-content: space-between"][style*="alignItems: center"] {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 1rem !important;
  }

  .registrations-tab div[style*="display: flex"][style*="justify-content: space-between"][style*="alignItems: center"] h3 {
    margin-bottom: 0 !important;
    font-size: 1.1rem !important;
  }

  /* Club events tab header */
  .club-events-tab div[style*="display: flex"][style*="justify-content: space-between"][style*="alignItems: center"] {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.5rem !important;
  }

  .club-events-tab div[style*="display: flex"][style*="justify-content: space-between"][style*="alignItems: center"] h3 {
    margin-bottom: 0 !important;
    font-size: 1.1rem !important;
  }

  .club-events-tab div[style*="display: flex"][style*="justify-content: space-between"][style*="alignItems: center"] p {
    margin-bottom: 0 !important;
    font-size: 0.85rem !important;
  }

  /* Fix back button positioning and visibility */
  .admin-club-details .back-button,
  .admin-event-details .back-button,
  button[style*="position: absolute"][style*="top: 1rem"][style*="left: 1rem"] {
    position: fixed !important;
    top: 1rem !important;
    left: 1rem !important;
    z-index: 1000 !important;
    background: var(--primary-color) !important;
    color: white !important;
    border: none !important;
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  }

  /* Ensure back button icon is visible */
  .admin-club-details .back-button svg,
  .admin-event-details .back-button svg,
  button[style*="position: absolute"] svg {
    width: 20px !important;
    height: 20px !important;
    color: white !important;
  }

  /* Add top padding to content to avoid back button overlap */
  .admin-club-details,
  .admin-event-details {
    padding-top: 60px !important;
  }

  /* Tab navigation - better mobile styling */
  .admin-club-details div[style*="display: flex"][style*="borderBottom: 1px solid"],
  .admin-event-details div[style*="display: flex"][style*="borderBottom: 1px solid"] {
    padding: 0 1rem !important;
    margin: 0 -1rem 1rem -1rem !important;
    background: var(--dark-surface) !important;
    border-radius: 8px 8px 0 0 !important;
  }

  /* Tab buttons */
  .admin-club-details div[style*="display: flex"][style*="borderBottom: 1px solid"] button,
  .admin-event-details div[style*="display: flex"][style*="borderBottom: 1px solid"] button {
    flex: 1 !important;
    padding: 1rem 0.5rem !important;
    font-size: 0.9rem !important;
    white-space: nowrap !important;
  }

  /* Force mobile layout for all flex containers */
  .admin-club-details div[style*="display: flex"]:not([style*="borderBottom"]):not([style*="position: absolute"]),
  .admin-event-details div[style*="display: flex"]:not([style*="borderBottom"]):not([style*="position: absolute"]) {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 0.75rem !important;
  }

  /* Better spacing for content sections */
  .admin-club-details > div,
  .admin-event-details > div {
    margin-bottom: 1rem !important;
  }

  /* Improve readability of long text content */
  .admin-club-details p,
  .admin-event-details p {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  /* Event cards in club events tab - ensure proper display */
  .club-events-tab > div:last-child {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
  }

  /* Individual event cards */
  .club-events-tab > div:last-child > div {
    background: var(--dark-surface) !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    margin-bottom: 0 !important;
  }

  /* Event card image containers */
  .club-events-tab img {
    width: 100% !important;
    height: 150px !important;
    object-fit: cover !important;
    border-radius: 6px !important;
    margin-bottom: 0.75rem !important;
  }

  /* Event badges and status indicators */
  .club-events-tab span[style*="backgroundColor: rgba"] {
    font-size: 0.75rem !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 4px !important;
    white-space: nowrap !important;
  }

  /* Event metadata (date, time, etc.) */
  .club-events-tab div[style*="display: flex"][style*="alignItems: center"][style*="gap: 0.5rem"] {
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
    margin: 0.5rem 0 !important;
  }

  /* Event action buttons */
  .club-events-tab button[style*="backgroundColor: var(--primary-color)"] {
    width: 100% !important;
    margin-top: 0.75rem !important;
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
  }

  /* Registration Details Component - Complete Mobile Optimization */
  .registration-details {
    width: 95% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 1rem !important;
    max-height: 95vh !important;
    border-radius: 8px !important;
  }

  /* Header section */
  .registration-details .header-section {
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 1rem !important;
  }

  .registration-details .header-section h2 {
    font-size: 1.2rem !important;
    margin: 0 !important;
  }

  .registration-details .close-button {
    font-size: 1.3rem !important;
    padding: 0.25rem !important;
    min-width: 30px !important;
    height: 30px !important;
  }

  /* Participant header section */
  .registration-details > div:nth-child(2) {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.75rem !important;
    text-align: left !important;
  }

  .registration-details > div:nth-child(2) > div:first-child {
    width: 100% !important;
  }

  .registration-details > div:nth-child(2) > div:last-child {
    align-self: flex-start !important;
  }

  /* All info grids - force single column */
  .registration-details .info-grid {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
  }

  /* Section titles */
  .registration-details .section-title,
  .registration-details h4 {
    font-size: 1rem !important;
    margin-bottom: 0.75rem !important;
    margin-top: 0 !important;
  }

  /* Info items */
  .registration-details .info-item {
    margin-bottom: 0.75rem !important;
  }

  .registration-details .info-item p:first-child {
    font-size: 0.8rem !important;
    margin-bottom: 0.25rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
  }

  .registration-details .info-item p:last-child {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
    word-break: break-word !important;
  }

  /* Custom fields container */
  .registration-details .custom-fields-container {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
  }

  /* Team members section */
  .registration-details .team-members-container {
    padding: 1rem !important;
  }

  .registration-details .team-member-card {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }

  .registration-details .team-member-details {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  /* Payment section */
  .registration-details .payment-section .info-grid {
    margin-bottom: 1rem !important;
  }

  .registration-details .payment-proof {
    width: 100% !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 0.75rem !important;
  }

  /* Timeline section */
  .registration-details .timeline-section .info-grid {
    gap: 0.75rem !important;
  }

  /* Form buttons */
  .registration-details .form-buttons {
    justify-content: center !important;
    margin-top: 1.5rem !important;
  }

  .registration-details .form-buttons button {
    flex: 1 !important;
    max-width: 200px !important;
    padding: 0.75rem 1rem !important;
  }

  /* Fix scrolling issues for detail pages and editors */
  .admin-club-details,
  .admin-event-details,
  .club-editor.full-page,
  .event-editor.full-page {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 999 !important;
    background: var(--dark-bg) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
  }

  /* Prevent background scroll when detail pages are open */
  body.modal-open {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
  }

  /* Content wrapper for detail pages and editors */
  .admin-club-details > *,
  .admin-event-details > *,
  .club-editor.full-page > *,
  .event-editor.full-page > * {
    position: relative !important;
    z-index: 1 !important;
  }

  /* Ensure proper scroll behavior for content sections */
  .admin-club-details > div:last-child,
  .admin-event-details > div:last-child {
    min-height: calc(100vh - 200px) !important;
    padding-bottom: 2rem !important;
  }

  /* Tab content areas - ensure proper scrolling */
  .club-events-tab,
  .registrations-tab,
  .event-details-tab {
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    max-height: calc(100vh - 250px) !important;
  }

  /* Registration table - better mobile scrolling */
  .registrations-tab div[style*="overflowX: auto"] {
    overflow-x: auto !important;
    overflow-y: visible !important;
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
  }

  /* Event cards container - smooth scrolling */
  .club-events-tab > div:last-child {
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
  }

  /* Registration Details Modal - Fix scrolling */
  .registration-details {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 1001 !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
    scroll-behavior: smooth !important;
  }

  /* Modal backdrop */
  .modal-backdrop {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: rgba(0, 0, 0, 0.7) !important;
    z-index: 1000 !important;
    backdrop-filter: blur(4px) !important;
  }

  /* Improve touch scrolling for all scrollable areas */
  .admin-club-details *,
  .admin-event-details *,
  .registration-details *,
  .club-editor.full-page *,
  .event-editor.full-page * {
    -webkit-overflow-scrolling: touch !important;
  }

  /* Prevent horizontal scroll issues */
  .admin-club-details,
  .admin-event-details,
  .registration-details,
  .club-editor.full-page,
  .event-editor.full-page {
    max-width: 100vw !important;
    box-sizing: border-box !important;
  }

  .admin-club-details *,
  .admin-event-details *,
  .registration-details *,
  .club-editor.full-page *,
  .event-editor.full-page * {
    box-sizing: border-box !important;
  }

  /* Better scroll indicators */
  .admin-club-details::-webkit-scrollbar,
  .admin-event-details::-webkit-scrollbar,
  .registration-details::-webkit-scrollbar {
    width: 4px !important;
  }

  .admin-club-details::-webkit-scrollbar-track,
  .admin-event-details::-webkit-scrollbar-track,
  .registration-details::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
  }

  .admin-club-details::-webkit-scrollbar-thumb,
  .admin-event-details::-webkit-scrollbar-thumb,
  .registration-details::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3) !important;
    border-radius: 2px !important;
  }

  /* Smooth scrolling for tab content */
  .admin-club-details div[style*="display: flex"][style*="borderBottom: 1px solid"] ~ div,
  .admin-event-details div[style*="display: flex"][style*="borderBottom: 1px solid"] ~ div {
    scroll-behavior: smooth !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* Additional touch scrolling improvements */
  .admin-club-details,
  .admin-event-details,
  .registration-details,
  .club-editor.full-page,
  .event-editor.full-page {
    touch-action: pan-y !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }

  /* Allow text selection in content areas */
  .admin-club-details p,
  .admin-event-details p,
  .registration-details p,
  .admin-club-details h1,
  .admin-event-details h1,
  .registration-details h1,
  .admin-club-details h2,
  .admin-event-details h2,
  .registration-details h2,
  .admin-club-details h3,
  .admin-event-details h3,
  .registration-details h3,
  .admin-club-details h4,
  .admin-event-details h4,
  .registration-details h4 {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
  }

  /* Prevent zoom on double tap for buttons */
  .admin-club-details button,
  .admin-event-details button,
  .registration-details button {
    touch-action: manipulation !important;
  }

  /* Fix for mobile scrolling - only apply fixed positioning on mobile */
  .admin-club-details,
  .admin-event-details,
  .club-editor.full-page,
  .event-editor.full-page {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
  }

  /* Allow direct scrolling on content */
  .admin-club-details,
  .admin-event-details,
  .club-editor.full-page,
  .event-editor.full-page {
    scroll-behavior: smooth !important;
    touch-action: pan-y !important;
  }

  /* Ensure proper height for content containers */
  .club-events-tab,
  .registrations-tab,
  .event-details-tab {
    height: calc(100vh - 200px) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
  }

  /* Fix registration table scrolling */
  .registrations-tab table {
    width: 100% !important;
    min-width: 600px !important;
  }

  /* Better mobile table handling */
  .registrations-tab div[style*="overflowX: auto"] {
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
    border-radius: 8px !important;
  }

  /* Ultra-specific overrides for stubborn layouts */
  .admin-club-details [style*="display: grid"][style*="gridTemplateColumns"],
  .admin-event-details [style*="display: grid"][style*="gridTemplateColumns"],
  .admin-club-details [style*="display:grid"][style*="gridTemplateColumns"],
  .admin-event-details [style*="display:grid"][style*="gridTemplateColumns"] {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
  }

  /* Force single column for any remaining grid layouts */
  .admin-club-details * {
    grid-template-columns: 1fr !important;
  }

  .admin-event-details * {
    grid-template-columns: 1fr !important;
  }

  /* Override any flex layouts that might be causing side-by-side */
  .admin-club-details div[style*="display: flex"]:not([style*="flex-direction: column"]):not([style*="borderBottom"]):not([style*="position: absolute"]),
  .admin-event-details div[style*="display: flex"]:not([style*="flex-direction: column"]):not([style*="borderBottom"]):not([style*="position: absolute"]) {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 1rem !important;
  }

  /* Ensure event cards display properly */
  .club-events-tab > div:last-child {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .club-events-tab > div:last-child > * {
    width: 100% !important;
    margin-bottom: 1rem !important;
  }

  /* Fix any remaining horizontal layouts */
  .admin-club-details > div:last-child > div,
  .admin-event-details > div:last-child > div {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
  }
}
