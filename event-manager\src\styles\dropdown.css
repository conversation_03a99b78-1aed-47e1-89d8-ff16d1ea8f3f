/* Dropdown/Select Styling */

/* Base select styling */
select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 100%;
  padding: 0.8rem 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: var(--text-primary);
  font-size: 1rem;
  cursor: pointer;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}

/* Focus state */
select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(110, 68, 255, 0.2);
}

/* Hover state */
select:hover {
  border-color: rgba(255, 255, 255, 0.3);
}

/* Option styling */
select option {
  background-color: #111111;
  color: var(--text-primary);
  padding: 0.8rem;
}

/* Disabled state */
select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Custom dropdown container for better positioning */
.custom-select-container {
  position: relative;
  width: 100%;
}

/* Styling for multi-select dropdowns */
.multi-select-container {
  position: relative;
  width: 100%;
}

.multi-select-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.8rem 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: var(--text-primary);
  cursor: pointer;
}

.multi-select-header:hover {
  border-color: rgba(255, 255, 255, 0.3);
}

.multi-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: #111111;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  z-index: 100;
  margin-top: 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.multi-select-option {
  padding: 0.8rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.multi-select-option:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.multi-select-option.selected {
  background-color: rgba(110, 68, 255, 0.2);
}

/* Selected tags display */
.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.selected-tag {
  display: inline-flex;
  align-items: center;
  background-color: rgba(110, 68, 255, 0.2);
  border-radius: 4px;
  padding: 0.3rem 0.6rem;
  font-size: 0.9rem;
}

.selected-tag button {
  background: none;
  border: none;
  color: var(--text-primary);
  margin-left: 0.5rem;
  cursor: pointer;
  font-size: 0.8rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Fix for dropdown options display */
select[multiple] {
  height: auto;
  min-height: 100px;
}

/* Fix for dropdown z-index issues */
.dropdown-wrapper {
  position: relative;
  z-index: 10;
}

/* Fix for dropdown option visibility */
select option {
  padding: 10px;
  background-color: #111111;
  color: white;
}

/* Fix for dropdown option hover */
select option:hover,
select option:focus,
select option:active,
select option:checked {
  background-color: var(--primary);
  color: black;
}
