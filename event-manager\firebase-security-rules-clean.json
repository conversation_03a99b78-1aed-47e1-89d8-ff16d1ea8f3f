{"rules": {".read": "true", "admins": {".read": "true", "$admin_id": {".write": "auth != null && auth.uid == $admin_id"}}, "clubs": {".read": "true", "$club_id": {".write": "auth != null && auth.uid == $club_id || root.child('admins').child(auth.uid).exists()"}}, "events": {".read": "true", ".write": "auth != null && (root.child('clubs').child(auth.uid).exists() || root.child('admins').child(auth.uid).exists())", ".indexOn": ["club_id", "status", "category_id", "start_date"]}, "registrations": {".read": "true", ".write": "true", ".indexOn": ["event_id", "user_id"]}, "categories": {".read": "true", ".write": "auth != null && root.child('admins').child(auth.uid).exists()", ".indexOn": ["name"]}, "club_requests": {".read": "auth != null && root.child('admins').child(auth.uid).exists()", ".write": "true", ".indexOn": ["status", "created_at"]}, "tags": {".read": "true", ".write": "auth != null && root.child('admins').child(auth.uid).exists()", ".indexOn": ["name"]}, "event_tags": {".read": "true", ".write": "auth != null", ".indexOn": [".key"]}}}