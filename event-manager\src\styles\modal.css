/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
  overflow: hidden; /* Prevent scrolling of the overlay itself */
}

.modal-content {
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative; /* Needed for proper scrolling */
  background-color: var(--dark-surface);
  border-radius: 10px;
  padding: 1.25rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  -webkit-overflow-scrolling: touch; /* For smooth scrolling on iOS */
}

/* Custom scrollbar for Webkit browsers */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Mobile Modal Optimizations */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 0.5rem;
    align-items: flex-start;
    padding-top: 2rem;
  }

  .modal-content {
    max-width: 100%;
    width: 100%;
    max-height: calc(100vh - 4rem);
    margin: 0;
    border-radius: 8px;
    padding: 1rem;
  }

  /* Club Profile Editor Mobile */
  .club-editor,
  .profile-editor {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 1rem !important;
  }

  .club-editor h2,
  .profile-editor h2 {
    font-size: 1.3rem !important;
    margin-bottom: 1rem;
    text-align: center;
  }

  .club-editor .form-group,
  .profile-editor .form-group {
    margin-bottom: 1.25rem !important;
  }

  .club-editor input,
  .club-editor textarea,
  .profile-editor input,
  .profile-editor textarea {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
  }

  .club-editor .social-links-section,
  .profile-editor .social-links-section {
    margin-top: 1.5rem;
  }

  .club-editor .social-link-group,
  .profile-editor .social-link-group {
    margin-bottom: 1rem;
  }

  .club-editor .form-actions,
  .profile-editor .form-actions {
    flex-direction: column !important;
    gap: 0.75rem !important;
    margin-top: 1.5rem;
  }

  .club-editor .form-actions button,
  .profile-editor .form-actions button {
    width: 100% !important;
    padding: 0.875rem !important;
    font-size: 1rem !important;
  }

  /* Admin Event Editor Mobile */
  .admin-event-editor {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 1rem !important;
  }

  .admin-event-editor h2 {
    font-size: 1.3rem !important;
    margin-bottom: 1rem;
    text-align: center;
  }

  .admin-event-editor .form-section {
    margin-bottom: 1.5rem !important;
  }

  .admin-event-editor .form-row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .admin-event-editor .form-group {
    width: 100% !important;
    margin-bottom: 1rem !important;
  }

  .admin-event-editor input,
  .admin-event-editor textarea,
  .admin-event-editor select {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
  }

  .admin-event-editor .image-upload-section {
    margin-bottom: 1.5rem;
  }

  .admin-event-editor .image-preview {
    max-width: 100% !important;
    height: auto !important;
    margin-bottom: 1rem;
  }

  .admin-event-editor .form-actions {
    flex-direction: column !important;
    gap: 0.75rem !important;
    margin-top: 1.5rem;
  }

  .admin-event-editor .form-actions button {
    width: 100% !important;
    padding: 0.875rem !important;
    font-size: 1rem !important;
  }

  /* Event Editor Mobile (Club Dashboard) */
  .event-editor {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 1rem !important;
  }

  .event-editor h2 {
    font-size: 1.3rem !important;
    margin-bottom: 1rem;
    text-align: center;
  }

  .event-editor .form-section {
    margin-bottom: 1.5rem !important;
  }

  .event-editor .form-row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .event-editor .form-group {
    width: 100% !important;
    margin-bottom: 1rem !important;
  }

  .event-editor input,
  .event-editor textarea,
  .event-editor select {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
  }

  .event-editor .schedule-section {
    margin-bottom: 1.5rem;
  }

  .event-editor .schedule-item {
    padding: 1rem !important;
    margin-bottom: 1rem;
  }

  .event-editor .schedule-event {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .event-editor .custom-fields-section {
    margin-bottom: 1.5rem;
  }

  .event-editor .custom-field-item {
    flex-direction: column !important;
    gap: 0.75rem !important;
    padding: 1rem !important;
  }

  .event-editor .form-actions {
    flex-direction: column !important;
    gap: 0.75rem !important;
    margin-top: 1.5rem;
  }

  .event-editor .form-actions button {
    width: 100% !important;
    padding: 0.875rem !important;
    font-size: 1rem !important;
  }
}

/* Small Mobile Devices Modal Optimization */
@media (max-width: 480px) {
  .modal-overlay {
    padding: 0.25rem;
    padding-top: 1rem;
  }

  .modal-content {
    max-height: calc(100vh - 2rem);
    padding: 0.75rem;
    border-radius: 6px;
  }

  /* Smaller form elements for very small screens */
  .club-editor h2,
  .profile-editor h2,
  .admin-event-editor h2,
  .event-editor h2 {
    font-size: 1.2rem !important;
    margin-bottom: 0.75rem;
  }

  .club-editor input,
  .club-editor textarea,
  .profile-editor input,
  .profile-editor textarea,
  .admin-event-editor input,
  .admin-event-editor textarea,
  .admin-event-editor select,
  .event-editor input,
  .event-editor textarea,
  .event-editor select {
    padding: 0.6rem !important;
    font-size: 0.95rem !important;
  }

  .club-editor .form-group,
  .profile-editor .form-group,
  .admin-event-editor .form-group,
  .event-editor .form-group {
    margin-bottom: 1rem !important;
  }

  .club-editor .form-actions button,
  .profile-editor .form-actions button,
  .admin-event-editor .form-actions button,
  .event-editor .form-actions button {
    padding: 0.75rem !important;
    font-size: 0.95rem !important;
  }

  /* Compact schedule and custom field items */
  .event-editor .schedule-item,
  .admin-event-editor .schedule-item {
    padding: 0.75rem !important;
  }

  .event-editor .custom-field-item,
  .admin-event-editor .custom-field-item {
    padding: 0.75rem !important;
  }

  /* Close button optimization */
  .modal-content button[style*="font-size: 1.5rem"] {
    font-size: 1.3rem !important;
    padding: 0.25rem !important;
  }
}

/* Very Small Screens (360px and below) */
@media (max-width: 360px) {
  .modal-content {
    padding: 0.5rem;
  }

  .club-editor h2,
  .profile-editor h2,
  .admin-event-editor h2,
  .event-editor h2 {
    font-size: 1.1rem !important;
  }

  .club-editor input,
  .club-editor textarea,
  .profile-editor input,
  .profile-editor textarea,
  .admin-event-editor input,
  .admin-event-editor textarea,
  .admin-event-editor select,
  .event-editor input,
  .event-editor textarea,
  .event-editor select {
    padding: 0.5rem !important;
    font-size: 0.9rem !important;
  }

  .club-editor .form-actions button,
  .profile-editor .form-actions button,
  .admin-event-editor .form-actions button,
  .event-editor .form-actions button {
    padding: 0.6rem !important;
    font-size: 0.9rem !important;
  }
}

/* Prevent body scrolling when modal is open */
body.modal-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
  touch-action: none; /* Disable touch events on the body */
}
