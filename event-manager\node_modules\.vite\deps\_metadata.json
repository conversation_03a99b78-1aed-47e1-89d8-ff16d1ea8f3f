{"hash": "8cf596be", "configHash": "c19d10e1", "lockfileHash": "89df49bd", "browserHash": "43e4b41b", "optimized": {"recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "2e4484b5", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "e0100b87", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "a02eb16f", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "df1b61e3", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "f7166c1e", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "e02e08dc", "needsInterop": true}, "@studio-freight/lenis": {"src": "../../@studio-freight/lenis/dist/lenis.mjs", "file": "@studio-freight_lenis.js", "fileHash": "c11ddf00", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.mjs", "file": "date-fns.js", "fileHash": "9e0ca00f", "needsInterop": false}, "exceljs": {"src": "../../exceljs/dist/exceljs.min.js", "file": "exceljs.js", "fileHash": "a3a6c653", "needsInterop": true}, "firebase/analytics": {"src": "../../firebase/analytics/dist/esm/index.esm.js", "file": "firebase_analytics.js", "fileHash": "5cb4287f", "needsInterop": false}, "firebase/app": {"src": "../../firebase/app/dist/esm/index.esm.js", "file": "firebase_app.js", "fileHash": "fa548e6d", "needsInterop": false}, "firebase/auth": {"src": "../../firebase/auth/dist/esm/index.esm.js", "file": "firebase_auth.js", "fileHash": "41fba2eb", "needsInterop": false}, "firebase/database": {"src": "../../firebase/database/dist/esm/index.esm.js", "file": "firebase_database.js", "fileHash": "ff1513e3", "needsInterop": false}, "gsap": {"src": "../../gsap/index.js", "file": "gsap.js", "fileHash": "491a73ee", "needsInterop": false}, "gsap/ScrollTrigger": {"src": "../../gsap/ScrollTrigger.js", "file": "gsap_ScrollTrigger.js", "fileHash": "c5a26647", "needsInterop": false}, "jspdf": {"src": "../../jspdf/dist/jspdf.es.min.js", "file": "jspdf.js", "fileHash": "a62cd317", "needsInterop": false}, "jspdf-autotable": {"src": "../../jspdf-autotable/dist/jspdf.plugin.autotable.mjs", "file": "jspdf-autotable.js", "fileHash": "126d22ee", "needsInterop": false}, "jsqr": {"src": "../../jsqr/dist/jsQR.js", "file": "jsqr.js", "fileHash": "21211243", "needsInterop": true}, "qrcode": {"src": "../../qrcode/lib/browser.js", "file": "qrcode.js", "fileHash": "e50b69cc", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "feb61459", "needsInterop": true}, "react-lazy-load-image-component": {"src": "../../react-lazy-load-image-component/build/index.js", "file": "react-lazy-load-image-component.js", "fileHash": "6da78e13", "needsInterop": true}, "xlsx": {"src": "../../xlsx/xlsx.mjs", "file": "xlsx.js", "fileHash": "0fb8ff5c", "needsInterop": false}}, "chunks": {"index.es-NPTH4IYH": {"file": "index__es-NPTH4IYH.js"}, "chunk-D7ZASVPN": {"file": "chunk-D7ZASVPN.js"}, "html2canvas.esm-Z3NAH5K6": {"file": "html2canvas__esm-Z3NAH5K6.js"}, "purify.es-4P3QSKBT": {"file": "purify__es-4P3QSKBT.js"}, "chunk-K27G2EXW": {"file": "chunk-K27G2EXW.js"}, "chunk-HJRYNCSI": {"file": "chunk-HJRYNCSI.js"}, "chunk-H3CBOMNX": {"file": "chunk-H3CBOMNX.js"}, "chunk-SIDXV47P": {"file": "chunk-SIDXV47P.js"}, "chunk-2TUXWMP5": {"file": "chunk-2TUXWMP5.js"}}}