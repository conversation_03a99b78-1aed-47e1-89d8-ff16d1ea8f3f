/* Mobile optimization for user-facing pages */

/* Mobile tab navigation styles */
.mobile-tabs {
  display: none;
}

/* General mobile adjustments */
@media (max-width: 768px) {
  .container {
    padding: 0 1.25rem;
  }

  /* Ensure event details container has proper padding */
  #event-details .container {
    padding: 0 0.75rem;
    max-width: 100%;
    overflow-x: hidden;
  }

  /* Ensure mobile event header has proper spacing */
  .mobile-event-header {
    padding: 0;
    margin: 0;
    width: 100%;
  }

  /* Style the mobile event title */
  .mobile-event-header h1 {
    font-size: 1.8rem !important;
    margin-top: 0 !important;
    margin-bottom: 1.2rem !important;
    text-align: center !important;
    padding: 0 0.5rem !important;
  }

  h1 {
    font-size: clamp(2.5rem, 8vw, 3.5rem);
  }

  h2 {
    font-size: clamp(1.8rem, 6vw, 2.5rem);
  }

  h3 {
    font-size: clamp(1.4rem, 5vw, 1.8rem);
  }

  .section {
    padding: 5rem 0;
  }

  .py-section {
    padding: 4rem 0;
  }

  /* Navbar mobile styles */
  .navbar-links {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    width: 40px;
    height: 40px;
    border-radius: 8px;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
  }

  .mobile-menu-btn:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  /* Mobile menu */
  .mobile-menu {
    width: 85%;
    max-width: 320px;
    padding: 2rem 1.5rem;
    overflow-y: auto;
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.3);
  }

  .mobile-menu-close {
    background: rgba(255, 255, 255, 0.05);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
  }

  .mobile-menu-close:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .mobile-menu-link {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: block;
    transition: color 0.3s ease, padding-left 0.3s ease;
  }

  .mobile-menu-link:hover,
  .mobile-menu-link:active,
  .mobile-menu-link.active {
    color: var(--accent);
    padding-left: 0.5rem;
  }

  .mobile-menu-link.active {
    font-weight: 600;
    position: relative;
  }

  .mobile-menu-link.active::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 3px;
    height: 100%;
    background: var(--accent);
    border-radius: 0 3px 3px 0;
  }

  .mobile-menu .btn {
    margin-top: 1rem;
    width: 100%;
    text-align: center;
  }

  /* Hero section */
  .hero-content {
    text-align: center;
    padding: 0 1rem;
  }

  .hero-cta {
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .hero-cta .btn {
    width: 100%;
    text-align: center;
  }

  /* About section */
  .about-content {
    flex-direction: column;
    gap: 2rem;
  }

  /* Events grid */
  .events-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  /* Club items */
  .club-item {
    min-width: 100%;
    padding: 1.5rem;
  }

  /* Footer */
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

/* Event details page mobile styles */
@media (max-width: 992px) {
  .event-content-grid {
    grid-template-columns: 1fr !important;
  }

  /* Mobile tab navigation */
  .mobile-tabs {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
    background-color: var(--dark-surface);
    border-radius: 8px;
    margin-bottom: 1.5rem;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .mobile-tab {
    padding: 1rem 1.2rem;
    font-size: 0.9rem;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    position: relative;
    flex-shrink: 0;
  }

  .mobile-tab.active {
    color: var(--text-primary);
    font-weight: 600;
  }

  .mobile-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary);
    border-radius: 3px 3px 0 0;
  }

  .mobile-tab-content {
    display: none;
  }

  .mobile-tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  /* Hide desktop sidebar in mobile view when tabs are active */
  .tabs-active .event-details-sidebar {
    display: none;
  }

  /* Default sidebar behavior */
  .event-details-sidebar {
    position: static !important;
    top: auto !important;
    width: 100% !important;
    margin-top: 2rem;
  }

  /* Event tabs */
  .event-tabs {
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 0.5rem;
  }

  .event-tab {
    padding: 0.8rem 1.2rem;
    font-size: 0.9rem;
  }
}

/* Social sharing buttons */
.social-share-buttons {
  display: flex;
  gap: 1.2rem;
  justify-content: center;
  position: relative;
  flex-wrap: wrap;
}

.social-share-button {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
  .social-share-buttons {
    gap: 0.8rem;
  }

  .social-share-button {
    width: 40px;
    height: 40px;
  }

  /* Club details page */
  .club-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .club-logo {
    margin-bottom: 1.5rem;
  }

  .club-content-grid {
    grid-template-columns: 1fr !important;
  }

  .club-sidebar {
    margin-top: 2rem;
  }

  .club-events-grid {
    grid-template-columns: 1fr;
  }

  .team-members-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
  }

  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)) !important;
  }

  /* Contact page */
  .contact-grid {
    grid-template-columns: 1fr;
  }

  .contact-form {
    order: 2;
  }

  .contact-info {
    order: 1;
    margin-bottom: 2rem;
  }

  /* Club Request Form Mobile Optimization */
  .club-request-form {
    padding: 1rem !important;
    margin: 0 !important;
  }

  .club-request-form .container {
    padding: 0 1rem;
  }

  .club-request-form h2 {
    font-size: 1.5rem !important;
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .club-request-form form {
    max-width: 100% !important;
  }

  .club-request-form .form-group {
    margin-bottom: 1.25rem !important;
  }

  .club-request-form input,
  .club-request-form textarea {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
    border-radius: 6px !important;
  }

  .club-request-form textarea {
    min-height: 100px !important;
    resize: vertical;
  }

  .club-request-form .file-upload-area {
    padding: 1rem !important;
    text-align: center;
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    margin-bottom: 1rem;
  }

  .club-request-form .logo-preview {
    max-width: 120px !important;
    max-height: 120px !important;
    margin: 0 auto 1rem;
  }

  .club-request-form .submit-button {
    width: 100% !important;
    padding: 0.875rem !important;
    font-size: 1rem !important;
    margin-top: 1rem;
  }

  /* Event Creation Form Mobile Optimization */
  .event-creation-form {
    padding: 1rem !important;
  }

  .event-creation-form .container {
    padding: 0 1rem;
    max-width: 100%;
  }

  .event-creation-form h2 {
    font-size: 1.5rem !important;
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .event-creation-form .form-section {
    margin-bottom: 2rem !important;
  }

  .event-creation-form .form-row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .event-creation-form .form-group {
    width: 100% !important;
    margin-bottom: 1.25rem !important;
  }

  .event-creation-form input,
  .event-creation-form textarea,
  .event-creation-form select {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
  }

  .event-creation-form .image-upload-section {
    margin-bottom: 1.5rem;
  }

  .event-creation-form .image-preview {
    max-width: 100% !important;
    height: auto !important;
    margin-bottom: 1rem;
  }

  .event-creation-form .schedule-item {
    padding: 1rem !important;
    margin-bottom: 1rem;
  }

  .event-creation-form .schedule-event {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .event-creation-form .custom-field-item {
    flex-direction: column !important;
    gap: 0.75rem !important;
    padding: 1rem !important;
  }

  .event-creation-form .tag-container {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .event-creation-form .tag-item {
    margin-bottom: 0.5rem;
  }

  /* Event Registration Form Mobile Optimization */
  .event-registration {
    padding: 1rem !important;
  }

  .event-registration .container {
    padding: 0 1rem;
  }

  .event-registration h2 {
    font-size: 1.4rem !important;
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .event-registration .registration-form {
    max-width: 100% !important;
  }

  .event-registration .form-section {
    margin-bottom: 1.5rem !important;
  }

  .event-registration .form-row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .event-registration .form-group {
    width: 100% !important;
    margin-bottom: 1rem !important;
  }

  .event-registration input,
  .event-registration select,
  .event-registration textarea {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
  }

  .event-registration .team-member-section {
    padding: 1rem !important;
    margin-bottom: 1rem;
  }

  .event-registration .team-member-fields {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .event-registration .payment-upload-section {
    text-align: center;
    padding: 1rem;
  }

  .event-registration .payment-preview {
    max-width: 200px !important;
    margin: 0 auto 1rem;
  }

  .event-registration .submit-button {
    width: 100% !important;
    padding: 0.875rem !important;
    font-size: 1rem !important;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .hero {
    height: auto;
    min-height: 100vh;
    padding: 6rem 0 4rem;
  }

  .event-card:hover {
    transform: translateY(-5px);
  }

  .club-item:hover {
    transform: translateY(-5px);
  }

  .btn {
    padding: 0.7em 1.5em;
    font-size: 0.95em;
  }

  /* Event details */
  .event-banner {
    height: 200px;
  }

  .event-title {
    font-size: 1.8rem;
  }

  .event-meta {
    flex-wrap: wrap;
  }

  .event-meta-item {
    margin-right: 0;
    margin-bottom: 0.5rem;
    width: 50%;
  }

  /* Event schedule */
  .event-schedule-item {
    grid-template-columns: 1fr 1fr !important;
  }

  .event-schedule-item > div:last-child {
    grid-column: span 2;
    margin-top: 0.5rem;
    font-size: 0.9rem;
  }

  /* Event highlights */
  .event-highlights-grid {
    grid-template-columns: 1fr !important;
  }

  /* Small screen optimizations for forms */
  .club-request-form h2,
  .event-creation-form h2,
  .event-registration h2 {
    font-size: 1.3rem !important;
  }

  .club-request-form input,
  .club-request-form textarea,
  .event-creation-form input,
  .event-creation-form textarea,
  .event-creation-form select,
  .event-registration input,
  .event-registration textarea,
  .event-registration select {
    padding: 0.6rem !important;
    font-size: 0.95rem !important;
  }

  .club-request-form .submit-button,
  .event-creation-form .submit-button,
  .event-registration .submit-button {
    padding: 0.75rem !important;
    font-size: 0.95rem !important;
  }

  /* Gallery grid for small screens */
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
    gap: 1rem !important;
  }

  /* Club events grid for small screens */
  .club-events-grid {
    gap: 1rem !important;
  }

  /* Team members grid for small screens */
  .team-members-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)) !important;
    gap: 0.75rem !important;
  }

  /* Attendance Management Mobile Optimization */
  .attendance-management {
    padding: 1rem !important;
  }

  .attendance-management .container {
    padding: 0 1rem;
  }

  .attendance-management h2 {
    font-size: 1.4rem !important;
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .attendance-management .event-selector {
    margin-bottom: 1.5rem;
  }

  .attendance-management .event-selector select {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
  }

  .attendance-management .stats-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
    margin-bottom: 1.5rem;
  }

  .attendance-management .stat-card {
    padding: 1rem !important;
    text-align: center;
  }

  .attendance-management .stat-card h3 {
    font-size: 1.1rem !important;
    margin-bottom: 0.5rem;
  }

  .attendance-management .stat-card .stat-value {
    font-size: 1.8rem !important;
  }

  .attendance-management .controls-section {
    margin-bottom: 1.5rem;
  }

  .attendance-management .controls-row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .attendance-management .scan-qr-button,
  .attendance-management .export-button {
    width: 100% !important;
    padding: 0.875rem !important;
    font-size: 1rem !important;
  }

  .attendance-management .registrations-list {
    margin-top: 1.5rem;
  }

  .attendance-management .registration-item {
    flex-direction: column !important;
    padding: 1rem !important;
    gap: 0.75rem !important;
  }

  .attendance-management .participant-info {
    width: 100% !important;
    text-align: center;
  }

  .attendance-management .participant-info h4 {
    font-size: 1rem !important;
    margin-bottom: 0.5rem;
  }

  .attendance-management .participant-details {
    flex-direction: column !important;
    gap: 0.25rem !important;
    text-align: center;
  }

  .attendance-management .attendance-info {
    width: 100% !important;
    text-align: center;
  }

  .attendance-management .attendance-status {
    flex-direction: column !important;
    gap: 0.5rem !important;
    align-items: center;
  }

  .attendance-management .mark-attendance-button {
    width: 100% !important;
    max-width: 200px !important;
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
  }

  .attendance-management .attendance-time {
    font-size: 0.8rem !important;
    margin-top: 0.25rem;
  }

  .attendance-management .recent-activity {
    margin-top: 1.5rem;
  }

  .attendance-management .recent-activity h3 {
    font-size: 1.2rem !important;
    margin-bottom: 1rem;
  }

  .attendance-management .activity-item {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem;
  }

  .attendance-management .activity-item .activity-details {
    flex-direction: column !important;
    gap: 0.25rem !important;
    text-align: center;
  }

  .attendance-management .success-message {
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
    text-align: center;
    margin-bottom: 1rem;
  }

  .attendance-management .error-message {
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
    text-align: center;
    margin-bottom: 1rem;
  }

  .attendance-management .loading-spinner {
    width: 16px !important;
    height: 16px !important;
  }

  /* Event Editor Mobile Optimization */
  .event-editor-container {
    padding: 1rem !important;
  }

  .event-editor-container .container {
    padding: 0 1rem;
    max-width: 100% !important;
  }

  .event-editor-container .header-section {
    flex-direction: column !important;
    gap: 1rem !important;
    text-align: center;
    padding: 1rem !important;
  }

  .event-editor-container h2 {
    font-size: 1.3rem !important;
    margin: 0;
  }

  .event-editor-container .back-button {
    width: 100% !important;
    max-width: 200px !important;
    padding: 0.75rem !important;
    font-size: 0.9rem !important;
  }

  .event-editor-container .form-section {
    margin-bottom: 1.5rem !important;
  }

  .event-editor-container .form-row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .event-editor-container .form-group {
    width: 100% !important;
    margin-bottom: 1rem !important;
  }

  .event-editor-container input,
  .event-editor-container textarea,
  .event-editor-container select {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
  }

  .event-editor-container .image-upload-section {
    margin-bottom: 1.5rem;
  }

  .event-editor-container .image-preview {
    max-width: 100% !important;
    height: auto !important;
    margin-bottom: 1rem;
  }

  .event-editor-container .schedule-section {
    margin-bottom: 1.5rem;
  }

  .event-editor-container .schedule-item {
    padding: 1rem !important;
    margin-bottom: 1rem;
  }

  .event-editor-container .schedule-event {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .event-editor-container .custom-fields-section {
    margin-bottom: 1.5rem;
  }

  .event-editor-container .custom-field-item {
    flex-direction: column !important;
    gap: 0.75rem !important;
    padding: 1rem !important;
  }

  .event-editor-container .form-actions {
    flex-direction: column !important;
    gap: 1rem !important;
    margin-top: 1.5rem;
  }

  .event-editor-container .form-actions button {
    width: 100% !important;
    padding: 0.875rem !important;
    font-size: 1rem !important;
  }

  /* Registration Details Mobile Optimization */
  .registration-details {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 1rem !important;
    max-height: calc(100vh - 2rem) !important;
  }

  .registration-details .header-section {
    flex-direction: column !important;
    gap: 1rem !important;
    text-align: center;
    margin-bottom: 1rem;
  }

  .registration-details h2 {
    font-size: 1.2rem !important;
    margin: 0;
  }

  .registration-details .close-button {
    align-self: center;
    width: 32px;
    height: 32px;
  }

  .registration-details .info-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .registration-details .info-item {
    text-align: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
  }

  .registration-details .info-item p:first-child {
    font-size: 0.8rem !important;
    margin-bottom: 0.5rem;
  }

  .registration-details .info-item p:last-child {
    font-size: 0.95rem !important;
  }

  .registration-details .section-title {
    font-size: 1rem !important;
    margin-bottom: 1rem;
    text-align: center;
  }

  .registration-details .team-members-section {
    margin-bottom: 1.5rem;
  }

  .registration-details .team-member-card {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem;
    text-align: center;
  }

  .registration-details .custom-fields-section {
    margin-bottom: 1.5rem;
  }

  .registration-details .custom-field-item {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem;
    text-align: center;
  }

  .registration-details .payment-section {
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .registration-details .payment-proof {
    max-width: 200px !important;
    margin: 0 auto 1rem;
  }

  .registration-details .timeline-section {
    margin-bottom: 1.5rem;
  }
}
