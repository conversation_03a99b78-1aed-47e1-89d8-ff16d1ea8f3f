{"rules": {".read": "true", "admins": {".read": "true", "$admin_id": {".write": "auth != null && auth.uid == $admin_id"}}, "clubs": {".read": "true", "$club_id": {".write": "auth != null && auth.uid == $club_id || root.child('admins').child(auth.uid).exists()"}}, "events": {".read": "true", ".write": "auth != null && (root.child('clubs').child(auth.uid).exists() || root.child('admins').child(auth.uid).exists())", ".indexOn": ["club_id", "status", "category_id", "start_date"], "$event_id": {"google_sheet_id": {".write": "true"}, "google_sheet_url": {".write": "true"}, "sheet_created_at": {".write": "true"}, "last_sync_at": {".write": "true"}, "last_sync_type": {".write": "true"}, "auto_sync_enabled": {".write": "true"}, "sync_error": {".write": "true"}, "updated_at": {".write": "true"}, "status": {".write": "true"}, "registration_open": {".write": "true"}, "is_archived": {".write": "true"}, "automation_last_run": {".write": "true"}, "automation_status": {".write": "true"}}}, "registrations": {".read": "true", ".write": "true", ".indexOn": ["event_id", "user_id"]}, "categories": {".read": "true", ".write": "auth != null && root.child('admins').child(auth.uid).exists()", ".indexOn": ["name"]}, "club_requests": {".read": "true", ".write": "true", ".indexOn": ["status", "created_at"]}, "tags": {".read": "true", ".write": "auth != null && root.child('admins').child(auth.uid).exists()", ".indexOn": ["name"]}, "event_tags": {".read": "true", ".write": "true", ".indexOn": [".key"]}, "automation_logs": {".read": "true", ".write": "true", ".indexOn": ["timestamp", "type", "status"]}, "automation_status": {".read": "true", ".write": "true"}, "event_live_updates": {".read": "true", ".indexOn": ["timestamp", "type", "is_pinned"], "$event_id": {".write": "auth != null && (root.child('clubs').child(auth.uid).exists() || root.child('admins').child(auth.uid).exists())", "$update_id": {".write": "auth != null && (root.child('clubs').child(auth.uid).exists() || root.child('admins').child(auth.uid).exists())"}}}, "event_chat": {".read": "true", ".indexOn": ["timestamp", "user_hash", "is_moderated"], "$event_id": {".write": "true", "$message_id": {".write": "true"}}}, "anonymous_sessions": {".read": "true", ".write": "true", ".indexOn": ["last_active", "is_muted"], "$event_id": {"$session_id": {".write": "true"}}}}}