/* Information Board Styles */

.information-board {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  color: white;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

.information-board.loading {
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.board-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.board-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.scroll-to-latest-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.scroll-to-latest-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.post-update-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.post-update-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.error-message {
  background: rgba(255, 107, 107, 0.9);
  border: 1px solid #ff6b6b;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  margin-left: 10px;
}

.post-update-form {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.form-group {
  margin-bottom: 15px;
}

.update-type-select {
  width: 100%;
  padding: 10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
}

.update-type-select option {
  background: #333;
  color: white;
}

.post-update-form textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  resize: vertical;
  min-height: 80px;
}

.post-update-form textarea::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.char-count {
  text-align: right;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 5px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
}

.post-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.post-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.post-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.updates-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
  margin-bottom: 15px;
}

.updates-container::-webkit-scrollbar {
  width: 6px;
}

.updates-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.updates-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.no-updates {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.update-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 12px;
  backdrop-filter: blur(10px);
  border-left: 4px solid transparent;
  transition: all 0.3s ease;
}

.update-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.update-item.pinned {
  background: rgba(255, 215, 0, 0.2);
  border-left-color: #ffd700;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.update-info {
  border-left-color: #2196F3;
}

.update-announcement {
  border-left-color: #FF9800;
}

.update-schedule {
  border-left-color: #4CAF50;
}

.update-important {
  border-left-color: #F44336;
  background: rgba(244, 67, 54, 0.1);
}

.update-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.update-icon {
  font-size: 1.2rem;
  margin-right: 10px;
}

.update-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.update-meta strong {
  color: #ffd700;
}

.timestamp {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

.pinned-badge {
  background: rgba(255, 215, 0, 0.3);
  color: #ffd700;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.edited-badge {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  font-style: italic;
}

.update-actions {
  display: flex;
  gap: 5px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 5px 8px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.delete-btn:hover {
  background: rgba(244, 67, 54, 0.7);
}

.update-message {
  line-height: 1.5;
  font-size: 1rem;
  word-wrap: break-word;
}

.board-footer {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .information-board {
    margin: 10px 0;
    padding: 15px;
    border-radius: 12px;
  }

  .board-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .board-header h3 {
    font-size: 1.2rem;
    text-align: center;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .scroll-to-latest-btn,
  .post-update-btn {
    width: 100%;
    text-align: center;
  }

  .update-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .update-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .update-actions {
    align-self: flex-end;
  }

  .post-update-form {
    padding: 15px;
  }

  .form-actions {
    justify-content: center;
  }

  .post-btn {
    width: 100%;
  }
}
