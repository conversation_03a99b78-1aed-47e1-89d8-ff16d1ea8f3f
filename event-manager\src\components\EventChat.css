/* Event Chat Styles */

.event-chat {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  color: white;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

.event-chat.loading {
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.chat-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.chat-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  font-size: 0.9rem;
}

.active-users {
  color: #4CAF50;
  font-weight: 500;
}

.current-user {
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-user strong {
  color: #ffd700;
}

.change-nickname-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 4px 6px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.change-nickname-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.error-message {
  background: rgba(255, 107, 107, 0.9);
  border: 1px solid #ff6b6b;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  margin-left: 10px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  scroll-behavior: smooth;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.no-messages {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.no-messages small {
  display: block;
  margin-top: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.message {
  display: flex;
  gap: 12px;
  animation: slideIn 0.3s ease-out;
}

.message.my-message {
  flex-direction: row-reverse;
}

.message.my-message .message-content {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-avatar {
  flex-shrink: 0;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
}

.message-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px;
  max-width: 70%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.nickname {
  font-weight: 600;
  color: #ffd700;
  font-size: 0.9rem;
}

.timestamp {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.admin-actions {
  display: flex;
  gap: 5px;
}

.moderate-btn,
.delete-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.moderate-btn:hover {
  background: rgba(255, 193, 7, 0.7);
}

.delete-btn:hover {
  background: rgba(244, 67, 54, 0.7);
}

.message-text {
  line-height: 1.4;
  word-wrap: break-word;
  margin-bottom: 8px;
}

.message-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.reactions {
  display: flex;
  gap: 8px;
}

.reaction-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.reaction-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.report-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
  padding: 4px 8px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.report-btn:hover {
  background: rgba(244, 67, 54, 0.3);
  color: white;
}

.message-input-form {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 15px;
  backdrop-filter: blur(10px);
}

.input-container {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
}

.input-container input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
}

.input-container input:focus {
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.input-container input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.input-container input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 12px 16px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.send-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.input-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.char-count {
  font-weight: 500;
}

.chat-footer {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .event-chat {
    margin: 10px 0;
    padding: 15px;
    border-radius: 12px;
    max-height: 500px;
  }

  .chat-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .chat-header h3 {
    font-size: 1.2rem;
    text-align: center;
  }

  .chat-info {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .message-content {
    max-width: 85%;
  }

  .message-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .admin-actions {
    align-self: flex-end;
  }

  .reactions {
    flex-wrap: wrap;
    gap: 6px;
  }

  .reaction-btn {
    font-size: 0.7rem;
    padding: 3px 6px;
  }

  .input-container {
    gap: 8px;
  }

  .input-container input {
    font-size: 0.9rem;
    padding: 10px 14px;
  }

  .send-btn {
    width: 44px;
    height: 44px;
    font-size: 1.1rem;
  }

  .input-info {
    font-size: 0.7rem;
  }
}

/* Nickname Modal Styles */
.nickname-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.nickname-modal {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 30px;
  max-width: 400px;
  width: 90%;
  color: white;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.nickname-modal h3 {
  margin: 0 0 10px 0;
  font-size: 1.4rem;
  text-align: center;
}

.nickname-modal p {
  margin: 0 0 20px 0;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1.4;
}

.nickname-input-group {
  margin-bottom: 15px;
}

.nickname-input-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
}

.nickname-input-group input:focus {
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.15);
}

.nickname-input-group input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.nickname-char-count {
  text-align: right;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 5px;
}

.nickname-error {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.5);
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 15px;
  color: #ffcccb;
  font-size: 0.9rem;
  text-align: center;
}

.nickname-modal-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 15px;
}

.skip-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 16px;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.skip-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.save-nickname-btn {
  flex: 1;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 12px 16px;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.save-nickname-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.save-nickname-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.nickname-tips {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Mobile Responsive for Modal */
@media (max-width: 768px) {
  .nickname-modal {
    padding: 20px;
    margin: 20px;
  }

  .nickname-modal h3 {
    font-size: 1.2rem;
  }

  .nickname-modal-actions {
    flex-direction: column;
  }

  .skip-btn,
  .save-nickname-btn {
    width: 100%;
  }
}
