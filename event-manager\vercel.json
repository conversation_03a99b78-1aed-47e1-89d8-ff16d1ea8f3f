{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/static-build"}, {"src": "api/**/*.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1", "headers": {"Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET,OPTIONS,PATCH,DELETE,POST,PUT", "Access-Control-Allow-Headers": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version"}}, {"handle": "filesystem"}, {"src": "/(.*)", "dest": "/index.html"}], "crons": [{"path": "/api/automation/trigger", "schedule": "0 8 * * *"}], "env": {}}