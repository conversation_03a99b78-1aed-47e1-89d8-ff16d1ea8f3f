.analyticsCharts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.universalHeader {
  grid-column: 1 / -1;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.universalHeader h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.3em;
  font-weight: 600;
}

.universalPeriodSelector {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.selectorLabel {
  font-weight: 500;
  color: #495057;
  font-size: 0.9em;
}

.chartContainer {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  min-height: 300px;
}

.chartContainer:hover {
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.chartContainer h4 {
  margin: 0 0 20px 0;
  color: #2d3748;
  font-size: 1.2em;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chartHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.chartHeader h4 {
  margin: 0;
  color: #2d3748;
}

.periodSelector {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.periodBtn {
  padding: 6px 12px;
  border: 1px solid #e0e0e0;
  background: #ffffff;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85em;
  font-weight: 500;
  transition: all 0.2s ease;
}

.periodBtn:hover {
  background: #6c5ce7;
  color: #ffffff;
  border-color: #6c5ce7;
}

.periodBtn.active {
  background: #6c5ce7;
  color: #ffffff;
  border-color: #6c5ce7;
}

.periodBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.noData {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  font-style: italic;
  font-size: 1em;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.statCard {
  text-align: center;
  padding: 15px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.statValue {
  display: block;
  font-size: 1.8em;
  font-weight: bold;
  color: #6c5ce7;
  margin-bottom: 5px;
}

.statLabel {
  display: block;
  font-size: 0.85em;
  color: #495057;
  font-weight: 500;
}

.tooltip {
  background: rgba(0, 0, 0, 0.9) !important;
  border: 1px solid #333 !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  color: white !important;
  font-size: 14px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

.tooltipLabel {
  margin: 0 0 4px 0 !important;
  font-weight: 500 !important;
  color: #ffffff !important;
}

.tooltipValue {
  margin: 0 !important;
  font-weight: 600 !important;
  color: #74b9ff !important;
}

.tooltipDate {
  margin: 4px 0 0 0 !important;
  font-size: 0.85em !important;
  color: #cccccc !important;
  font-weight: 400 !important;
}

/* Responsive design for tablets */
@media (max-width: 768px) {
  .analyticsCharts {
    grid-template-columns: 1fr;
    gap: 15px;
    margin: 0 10px;
  }

  .universalHeader {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    padding: 15px;
  }

  .universalPeriodSelector {
    justify-content: center;
    gap: 8px;
  }

  .chartContainer {
    padding: 18px;
    min-height: 280px;
  }

  .chartHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .chartHeader h4 {
    font-size: 1.1em;
    text-align: center;
  }

  .periodSelector {
    justify-content: center;
    gap: 6px;
  }

  .periodBtn {
    flex: 1;
    min-width: 55px;
    font-size: 0.8em;
    padding: 8px 10px;
  }

  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .statCard {
    padding: 12px;
  }

  .statValue {
    font-size: 1.4em;
  }

  .statLabel {
    font-size: 0.8em;
  }
}

/* Mobile phone optimization */
@media (max-width: 480px) {
  .analyticsCharts {
    margin: 0 5px;
    gap: 12px;
    grid-template-columns: 1fr;
  }

  .universalHeader {
    padding: 12px;
    margin-bottom: 8px;
  }

  .universalHeader h3 {
    font-size: 1.1em;
    margin-bottom: 10px;
  }

  .selectorLabel {
    font-size: 0.8em;
    margin-bottom: 5px;
  }

  .chartContainer {
    padding: 15px;
    min-height: 250px;
    border-radius: 8px;
  }

  .chartHeader {
    gap: 10px;
  }

  .chartHeader h4 {
    font-size: 1em;
    margin-bottom: 8px;
  }

  .periodSelector {
    gap: 4px;
    flex-wrap: wrap;
  }

  .periodBtn {
    font-size: 0.75em;
    padding: 6px 8px;
    min-width: 50px;
    border-radius: 4px;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .statCard {
    padding: 10px;
    border-radius: 6px;
  }

  .statValue {
    font-size: 1.2em;
    margin-bottom: 3px;
  }

  .statLabel {
    font-size: 0.75em;
  }

  .noData {
    height: 120px;
    font-size: 0.85em;
    padding: 20px;
  }
}

/* Extra small phones */
@media (max-width: 360px) {
  .analyticsCharts {
    margin: 0 2px;
    gap: 10px;
  }

  .chartContainer {
    padding: 12px;
    min-height: 220px;
  }

  .chartHeader h4 {
    font-size: 0.9em;
  }

  .periodBtn {
    font-size: 0.7em;
    padding: 5px 6px;
    min-width: 45px;
  }

  .statCard {
    padding: 8px;
  }

  .statValue {
    font-size: 1.1em;
  }

  .statLabel {
    font-size: 0.7em;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .chartContainer {
    background: #2d3748;
    border-color: #4a5568;
  }

  .chartContainer h4,
  .chartHeader h4 {
    color: #f7fafc;
  }

  .periodBtn {
    background: #4a5568;
    color: #e2e8f0;
    border-color: #4a5568;
  }

  .periodBtn:hover,
  .periodBtn.active {
    background: #6c5ce7;
    color: #ffffff;
    border-color: #6c5ce7;
  }

  .statCard {
    background: #4a5568;
    border-color: #4a5568;
  }

  .statLabel {
    color: #cbd5e0;
  }

  .noData {
    color: #a0aec0;
  }
}

/* Animation for chart containers */
.chartContainer {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar for overflow */
.analyticsCharts::-webkit-scrollbar {
  width: 8px;
}

.analyticsCharts::-webkit-scrollbar-track {
  background: var(--bg-secondary, #f1f5f9);
  border-radius: 4px;
}

.analyticsCharts::-webkit-scrollbar-thumb {
  background: var(--primary-color, #6c5ce7);
  border-radius: 4px;
}

.analyticsCharts::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color-dark, #5a4fcf);
}

/* Override Recharts default tooltip styling */
:global(.recharts-tooltip-wrapper) {
  z-index: 1000 !important;
}

:global(.recharts-default-tooltip) {
  background: rgba(0, 0, 0, 0.9) !important;
  border: 1px solid #333 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

:global(.recharts-tooltip-label) {
  color: #ffffff !important;
  font-weight: 500 !important;
  margin-bottom: 4px !important;
}

:global(.recharts-tooltip-item) {
  color: #74b9ff !important;
  font-weight: 600 !important;
}

:global(.recharts-tooltip-item-name) {
  color: #ffffff !important;
}

:global(.recharts-tooltip-item-value) {
  color: #74b9ff !important;
  font-weight: 600 !important;
}
