/* Mobile styles for Club Dashboard */

/* Loading animation for export buttons */
@keyframes loadingPulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.export-buttons button[disabled] span {
  animation: loadingPulse 1s ease-in-out infinite;
  display: inline-block;
}

/* General mobile adjustments */
@media (max-width: 768px) {
  .club-dashboard .container {
    padding: 0 1rem;
  }

  /* Dashboard header */
  .dashboard-header {
    flex-direction: column;
    padding: 1.25rem !important;
  }

  .dashboard-header-content {
    flex-direction: column;
    align-items: center !important;
    text-align: center;
    margin-bottom: 1rem;
  }

  .dashboard-header-actions {
    flex-direction: column;
    width: 100%;
    gap: 0.75rem !important;
  }

  .dashboard-header-actions button {
    width: 100%;
  }

  /* Dashboard tabs */
  .dashboard-tabs {
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem !important;
  }

  .tab-button {
    padding: 0.75rem 1rem !important;
    font-size: 0.9rem;
  }

  /* Events filters */
  .events-filters {
    flex-direction: column;
    gap: 1rem;
    padding: 0.75rem !important;
  }

  .events-filters-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    width: 100%;
  }

  .events-filters-buttons button {
    padding: 0.4rem 0.5rem !important;
    font-size: 0.85rem;
  }

  .events-filters-search {
    width: 100%;
  }

  .events-filters-search input {
    width: 100%;
  }

  /* Event items */
  .event-item {
    flex-direction: column !important;
  }

  .event-image {
    width: 100% !important;
    height: 150px !important;
  }

  .event-content {
    padding: 1rem !important;
  }

  .event-header {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .event-header span {
    margin-top: 0.5rem;
  }

  .event-actions {
    flex-direction: column;
    width: 100%;
    gap: 0.5rem !important;
  }

  .event-actions button {
    width: 100%;
  }

  /* Registration section */
  .registration-select {
    margin-bottom: 1rem;
  }

  /* Registration table responsive styles */
  .table-scroll-container {
    -webkit-overflow-scrolling: touch; /* For smooth scrolling on iOS */
    scrollbar-width: thin;
    scrollbar-color: var(--accent) rgba(255, 255, 255, 0.1);
  }

  .table-scroll-container::-webkit-scrollbar {
    height: 6px;
  }

  .table-scroll-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  .table-scroll-container::-webkit-scrollbar-thumb {
    background-color: var(--accent);
    border-radius: 3px;
  }

  /* Hide scroll indicator on desktop */
  @media (min-width: 1024px) {
    .scroll-indicator {
      display: none !important;
    }
  }

  /* Export buttons styling for mobile */
  @media (max-width: 768px) {
    .export-buttons {
      flex-direction: column;
      width: 100%;
      margin-top: 0.5rem;
    }

    .export-pdf-btn,
    .export-sheets-btn,
    .export-google-sheets-btn {
      margin-top: 0.5rem;
      width: 100% !important;
      justify-content: center !important;
      padding: 0.75rem 1rem !important;
      font-size: 0.9rem !important;
      white-space: nowrap !important;
    }
  }

  /* Adjust header and export button layout on mobile */
  @media (max-width: 768px) {
    div[style*="display: flex"][style*="justify-content: space-between"][style*="alignItems: center"] {
      flex-direction: column;
      align-items: flex-start !important;
    }

    div[style*="display: flex"][style*="justify-content: space-between"][style*="alignItems: center"] h3 {
      margin-bottom: 0.75rem !important;
    }

    /* Enhance table readability on mobile */
    .registration-table-wrapper {
      border-radius: 8px !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }

    .table-scroll-container {
      padding-bottom: 0.5rem;
    }

    .table-scroll-container table th,
    .table-scroll-container table td {
      padding: 0.75rem !important;
    }

    /* Add fading effect to indicate scrollable content */
    .table-scroll-container::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: 30px;
      background: linear-gradient(to right, transparent, var(--dark-surface));
      pointer-events: none;
      z-index: 1;
    }

    /* Add scroll animation to hint at scrollability */
    @keyframes scrollHint {
      0% { transform: translateX(0); }
      25% { transform: translateX(10px); }
      50% { transform: translateX(0); }
      75% { transform: translateX(10px); }
      100% { transform: translateX(0); }
    }

    /* Add loading animation for export buttons */
    @keyframes loadingPulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    button[disabled] span {
      animation: loadingPulse 1s ease-in-out infinite;
    }

    .scroll-indicator span {
      animation: scrollHint 2.5s ease-in-out infinite;
      display: inline-block;
    }
  }

  /* Ensure buttons are properly sized on very small screens */
  @media (max-width: 360px) {
    .registration-card button {
      font-size: 0.8rem !important;
      padding: 0.5rem !important;
    }

    .registration-card button span {
      font-size: 1rem !important;
    }

    .export-pdf-btn,
    .export-sheets-btn,
    .export-google-sheets-btn {
      padding: 0.6rem 0.8rem !important;
      font-size: 0.85rem !important;
    }

    .export-pdf-btn span,
    .export-sheets-btn span,
    .export-google-sheets-btn span {
      font-size: 1rem !important;
    }
  }

  /* Modals */
  .modal-content {
    width: 95% !important;
    max-width: none !important;
    padding: 1rem !important;
  }

  .modal-header {
    padding: 1rem !important;
  }

  .modal-form {
    padding: 1rem !important;
  }

  /* Form groups */
  .form-group {
    flex-direction: column !important;
  }

  .form-group > div {
    width: 100% !important;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .club-dashboard {
    padding: 1rem 0 !important;
  }

  .dashboard-header {
    padding: 1rem !important;
  }

  .tab-button {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.85rem;
  }

  .event-item {
    margin-bottom: 1rem !important;
  }

  .event-image {
    height: 120px !important;
  }

  .event-content {
    padding: 0.75rem !important;
  }

  .event-title {
    font-size: 1.2rem !important;
  }

  .event-description {
    font-size: 0.9rem;
    margin-bottom: 1rem !important;
  }

  .event-footer {
    padding-top: 0.75rem !important;
  }

  /* Registration table for small screens */
  .registration-table {
    margin: 0 -1rem;
    width: calc(100% + 2rem);
    border-radius: 0 !important;
  }

  /* Profile Editor */
  .profile-editor {
    padding: 1rem !important;
  }

  .profile-editor h2 {
    font-size: 1.1rem !important;
  }

  .profile-editor h3 {
    font-size: 1rem !important;
  }

  .form-buttons {
    justify-content: center !important;
  }

  .form-buttons button {
    width: 100%;
  }
}

/* Club Profile Editor Mobile Styles */
@media (max-width: 768px) {
  .social-links-grid {
    grid-template-columns: 1fr !important;
  }

  .form-group {
    margin-bottom: 1rem !important;
  }

  .profile-editor input,
  .profile-editor textarea,
  .event-editor input,
  .event-editor textarea,
  .event-editor select {
    font-size: 16px !important; /* Prevents iOS zoom on focus */
  }

  .profile-editor label,
  .event-editor label {
    font-size: 0.9rem !important;
  }

  .form-buttons {
    margin-top: 1.5rem;
  }

  /* Event Editor Specific Styles */
  .event-editor {
    padding: 1rem !important;
  }

  .event-editor h2 {
    font-size: 1.1rem !important;
  }

  .event-editor h3 {
    font-size: 1rem !important;
  }

  .event-editor .form-grid {
    grid-template-columns: 1fr !important;
  }

  .event-editor .tag-container {
    flex-wrap: wrap;
  }

  .event-editor .tag-item {
    margin-bottom: 0.5rem;
  }

  /* Registration Details Specific Styles */
  .registration-details {
    padding: 1rem !important;
  }

  .registration-details h2 {
    font-size: 1.1rem !important;
  }

  .registration-details h3 {
    font-size: 1rem !important;
  }

  .registration-details h4 {
    font-size: 0.95rem !important;
  }

  .registration-details .info-grid {
    grid-template-columns: 1fr !important;
  }

  /* Team member details for mobile */
  @media (max-width: 480px) {
    .registration-details .team-member-details {
      grid-template-columns: 1fr !important;
      gap: 0.25rem !important;
    }

    .registration-details .team-member-item {
      padding: 0.75rem 0.5rem !important;
    }
  }

  /* Custom Fields Section Mobile Styles */
  .custom-fields-section {
    margin-bottom: 2rem !important;
    padding: 1rem !important;
    background-color: rgba(255, 255, 255, 0.03) !important;
    border-radius: 8px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .custom-fields-section h3 {
    font-size: 1.2rem !important;
    margin-bottom: 1rem !important;
    color: var(--primary) !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .custom-field-item {
    margin-bottom: 1.5rem !important;
    padding: 1rem !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255, 255, 255, 0.08) !important;
  }

  .custom-field-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 1rem !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
  }

  .custom-field-header h4 {
    font-size: 1rem !important;
    margin: 0 !important;
    color: var(--text-primary) !important;
    flex: 1 !important;
    min-width: 0 !important;
  }

  .custom-field-remove-btn {
    padding: 0.4rem 0.8rem !important;
    font-size: 0.8rem !important;
    background: rgba(255, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 0, 0, 0.3) !important;
    color: #ff3333 !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    white-space: nowrap !important;
  }

  .custom-field-form {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .custom-field-row {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 0.75rem !important;
  }

  .custom-field-row .form-group {
    margin-bottom: 0 !important;
  }

  .custom-field-row .form-group label {
    font-size: 0.85rem !important;
    margin-bottom: 0.4rem !important;
    color: var(--text-secondary) !important;
    display: block !important;
  }

  .custom-field-row .form-group input,
  .custom-field-row .form-group select {
    padding: 0.6rem 0.8rem !important;
    font-size: 16px !important; /* Prevent iOS zoom */
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 4px !important;
    color: var(--text-primary) !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  .custom-field-options {
    margin-top: 1rem !important;
  }

  .custom-field-options h5 {
    font-size: 0.9rem !important;
    margin-bottom: 0.75rem !important;
    color: var(--text-secondary) !important;
  }

  .custom-field-option-item {
    display: flex !important;
    gap: 0.5rem !important;
    margin-bottom: 0.5rem !important;
    align-items: center !important;
  }

  .custom-field-option-item input {
    flex: 1 !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 16px !important; /* Prevent iOS zoom */
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 4px !important;
    color: var(--text-primary) !important;
  }

  .custom-field-option-remove {
    padding: 0.3rem 0.6rem !important;
    font-size: 0.7rem !important;
    background: rgba(255, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 0, 0, 0.3) !important;
    color: #ff3333 !important;
    border-radius: 3px !important;
    cursor: pointer !important;
    white-space: nowrap !important;
  }

  .add-custom-field-btn {
    width: 100% !important;
    padding: 0.8rem 1rem !important;
    background: rgba(110, 68, 255, 0.1) !important;
    border: 1px solid rgba(110, 68, 255, 0.3) !important;
    color: var(--primary) !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-size: 0.9rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    margin-top: 1rem !important;
  }

  /* Event Schedule Section Mobile Styles */
  .event-schedule-section {
    margin-bottom: 2rem !important;
    padding: 1rem !important;
    background-color: rgba(255, 255, 255, 0.03) !important;
    border-radius: 8px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .event-schedule-section h3 {
    font-size: 1.2rem !important;
    margin-bottom: 1rem !important;
    color: var(--primary) !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .schedule-day-item {
    margin-bottom: 1.5rem !important;
    padding: 1rem !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255, 255, 255, 0.08) !important;
  }

  .schedule-day-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 1rem !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
  }

  .schedule-day-header h4 {
    font-size: 1rem !important;
    margin: 0 !important;
    color: var(--text-primary) !important;
    flex: 1 !important;
    min-width: 0 !important;
  }

  .schedule-day-remove-btn {
    padding: 0.4rem 0.8rem !important;
    font-size: 0.8rem !important;
    background: rgba(255, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 0, 0, 0.3) !important;
    color: #ff3333 !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    white-space: nowrap !important;
  }

  .schedule-day-form {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .schedule-day-form .form-group {
    margin-bottom: 0 !important;
  }

  .schedule-day-form .form-group label {
    font-size: 0.85rem !important;
    margin-bottom: 0.4rem !important;
    color: var(--text-secondary) !important;
    display: block !important;
  }

  .schedule-day-form .form-group input {
    padding: 0.6rem 0.8rem !important;
    font-size: 16px !important; /* Prevent iOS zoom */
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 4px !important;
    color: var(--text-primary) !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Event Schedule Input Fields Mobile Styles */
  .event-schedule-section input[type="date"],
  .event-schedule-section input[type="time"],
  .event-schedule-section input[type="text"] {
    padding: 0.8rem 1rem !important;
    font-size: 16px !important; /* Prevent iOS zoom */
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    color: var(--text-primary) !important;
    width: 100% !important;
    box-sizing: border-box !important;
    min-height: 48px !important; /* Touch-friendly height */
  }

  .event-schedule-section input[type="date"]:focus,
  .event-schedule-section input[type="time"]:focus,
  .event-schedule-section input[type="text"]:focus {
    background-color: rgba(255, 255, 255, 0.15) !important;
    border-color: var(--primary) !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(110, 68, 255, 0.2) !important;
  }

  /* Schedule event row mobile layout */
  .schedule-event-row {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    margin-bottom: 1rem !important;
    padding: 1rem !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .schedule-event-time {
    width: 100% !important;
  }

  .schedule-event-title {
    width: 100% !important;
  }

  .schedule-event-location {
    width: 100% !important;
  }

  .schedule-event-remove {
    align-self: flex-end !important;
    margin-top: 0.5rem !important;
  }

  .add-schedule-day-btn {
    width: 100% !important;
    padding: 0.8rem 1rem !important;
    background: rgba(110, 68, 255, 0.1) !important;
    border: 1px solid rgba(110, 68, 255, 0.3) !important;
    color: var(--primary) !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-size: 0.9rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    margin-top: 1rem !important;
  }

  /* Schedule day header mobile layout */
  .schedule-day-header {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 1rem !important;
  }

  .schedule-date-section {
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  .schedule-date-section h4 {
    margin: 0 !important;
    font-size: 0.9rem !important;
    color: var(--text-secondary) !important;
    margin-bottom: 0.5rem !important;
  }

  .schedule-day-header > div:last-child {
    width: 100% !important;
    justify-content: flex-end !important;
  }

  /* Date input specific styling */
  .schedule-date-section input[type="date"] {
    padding: 0.8rem 1rem !important;
    font-size: 16px !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    color: var(--text-primary) !important;
    width: 100% !important;
    min-height: 48px !important;
    box-sizing: border-box !important;
  }

  .schedule-date-section input[type="date"]:focus {
    background-color: rgba(255, 255, 255, 0.15) !important;
    border-color: var(--primary) !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(110, 68, 255, 0.2) !important;
  }

  /* Small mobile specific adjustments */
  @media (max-width: 480px) {
    .custom-field-row {
      grid-template-columns: 1fr !important;
    }

    .custom-field-header {
      flex-direction: column !important;
      align-items: flex-start !important;
    }

    .custom-field-remove-btn,
    .schedule-day-remove-btn {
      align-self: flex-end !important;
      margin-top: 0.5rem !important;
    }

    .custom-field-option-item {
      flex-direction: column !important;
      align-items: stretch !important;
    }

    .custom-field-option-remove {
      align-self: flex-end !important;
      margin-top: 0.5rem !important;
    }

    /* Schedule event row becomes fully vertical on small screens */
    .schedule-event-row {
      flex-direction: column !important;
      align-items: stretch !important;
    }

    .schedule-event-time,
    .schedule-event-title,
    .schedule-event-location {
      width: 100% !important;
      flex: none !important;
    }

    .schedule-event-remove {
      align-self: flex-end !important;
      margin-top: 1rem !important;
    }
  }
}
