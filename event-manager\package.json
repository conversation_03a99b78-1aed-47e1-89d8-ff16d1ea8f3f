{"name": "event-manager", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test:automation": "node test-automation.js"}, "dependencies": {"@cloudinary/url-gen": "^1.21.0", "@prisma/client": "^6.6.0", "@react-three/drei": "^10.0.6", "@react-three/fiber": "^9.1.2", "@studio-freight/lenis": "^1.0.42", "@vercel/node": "^5.1.15", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "bcryptjs": "^3.0.2", "date-fns": "^3.6.0", "docker": "^1.0.0", "dotenv": "^16.5.0", "dynamsoft-javascript-barcode": "^9.6.42", "exceljs": "^4.4.0", "express": "^5.1.0", "firebase": "^10.14.1", "form-data": "^4.0.2", "framer-motion": "^12.7.4", "googleapis": "^148.0.0", "gsap": "^3.12.7", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jsqr": "^1.4.0", "node-fetch": "^3.3.2", "prisma": "^6.6.0", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-lazy-load-image-component": "^1.6.3", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "three": "^0.175.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}