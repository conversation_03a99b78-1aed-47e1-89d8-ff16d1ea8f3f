/* Collage Background Styles */
.collage-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
  }
  
  .collage-container {
    position: relative;
    width: 100%;
    height: 100%;
    opacity: 0.45; /* 45% visibility as requested */
  }
  
  .collage-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0.5));
    z-index: 1;
  }
  
  .collage-item {
    position: absolute;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transition: transform 0.5s ease;
  }
  
  /* Desktop Layout */
  @media (min-width: 768px) {
    .collage-item:nth-child(1) {
      top: 0%;
      left: 0%;
      width: 35%;
      height: 40%;
      z-index: 2;
      transform: rotate(-2deg);
    }
    
    .collage-item:nth-child(2) {
      top: 5%;
      left: 30%;
      width: 30%;
      height: 35%;
      z-index: 3;
      transform: rotate(1deg);
    }
    
    .collage-item:nth-child(3) {
      top: 0%;
      right: 5%;
      width: 30%;
      height: 45%;
      z-index: 2;
      transform: rotate(2deg);
    }
    
    .collage-item:nth-child(4) {
      top: 35%;
      left: 5%;
      width: 28%;
      height: 40%;
      z-index: 4;
      transform: rotate(-1deg);
    }
    
    .collage-item:nth-child(5) {
      top: 40%;
      left: 35%;
      width: 32%;
      height: 38%;
      z-index: 5;
      transform: rotate(1.5deg);
    }
    
    .collage-item:nth-child(6) {
      top: 45%;
      right: 8%;
      width: 25%;
      height: 42%;
      z-index: 3;
      transform: rotate(-2deg);
    }
    
    .collage-item:nth-child(7) {
      bottom: 5%;
      left: 2%;
      width: 30%;
      height: 35%;
      z-index: 2;
      transform: rotate(1deg);
    }
    
    .collage-item:nth-child(8) {
      bottom: 0%;
      left: 33%;
      width: 28%;
      height: 30%;
      z-index: 3;
      transform: rotate(-1.5deg);
    }
    
    .collage-item:nth-child(9) {
      bottom: 8%;
      right: 5%;
      width: 32%;
      height: 32%;
      z-index: 4;
      transform: rotate(2deg);
    }
  }
  
  /* Mobile Layout */
  @media (max-width: 767px) {
    .collage-container {
      opacity: 0.4; /* Slightly more transparent on mobile */
    }
    
    .collage-item:nth-child(1) {
      top: 0%;
      left: 0%;
      width: 50%;
      height: 25%;
      z-index: 2;
    }
    
    .collage-item:nth-child(2) {
      top: 0%;
      right: 0%;
      width: 50%;
      height: 25%;
      z-index: 3;
    }
    
    .collage-item:nth-child(3) {
      top: 25%;
      left: 0%;
      width: 50%;
      height: 25%;
      z-index: 2;
    }
    
    .collage-item:nth-child(4) {
      top: 25%;
      right: 0%;
      width: 50%;
      height: 25%;
      z-index: 4;
    }
    
    .collage-item:nth-child(5) {
      top: 50%;
      left: 0%;
      width: 50%;
      height: 25%;
      z-index: 5;
    }
    
    .collage-item:nth-child(6) {
      top: 50%;
      right: 0%;
      width: 50%;
      height: 25%;
      z-index: 3;
    }
    
    .collage-item:nth-child(7) {
      top: 75%;
      left: 0%;
      width: 33.33%;
      height: 25%;
      z-index: 2;
    }
    
    .collage-item:nth-child(8) {
      top: 75%;
      left: 33.33%;
      width: 33.33%;
      height: 25%;
      z-index: 3;
    }
    
    .collage-item:nth-child(9) {
      top: 75%;
      right: 0%;
      width: 33.33%;
      height: 25%;
      z-index: 4;
    }
    
    /* Remove rotations on mobile for cleaner layout */
    .collage-item {
      transform: rotate(0deg) !important;
    }
  }
  
  /* Subtle hover effect for desktop */
  @media (min-width: 768px) {
    .collage-item:hover {
      transform: scale(1.03) rotate(0deg);
      z-index: 10;
    }
  }
  
  /* Add a subtle blur effect to ensure content readability */
  .collage-container {
    filter: blur(1px);
  }
  
  /* Animation for a subtle zoom effect on page load */
  @keyframes slowZoom {
    from { transform: scale(1); }
    to { transform: scale(1.05); }
  }
  
  .collage-container {
    animation: slowZoom 30s ease-in-out infinite alternate;
  }