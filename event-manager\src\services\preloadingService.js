import eventService from './eventService';
import clubService from './clubService';
import logger from '../utils/logger';

/**
 * Preloading Service
 * Manages background loading of top events and clubs for better performance
 */
class PreloadingService {
  constructor() {
    this.cache = {
      topEvents: null,
      topClubs: null,
      lastUpdated: {
        events: null,
        clubs: null
      }
    };
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.isPreloading = false;
  }

  /**
   * Check if cached data is still valid
   */
  isCacheValid(type) {
    const lastUpdated = this.cache.lastUpdated[type];
    if (!lastUpdated) return false;
    return Date.now() - lastUpdated < this.cacheTimeout;
  }

  /**
   * Preload top events in background
   */
  async preloadTopEvents(limit = 10) {
    try {
      if (this.isCacheValid('events') && this.cache.topEvents) {
        logger.log('Using cached top events');
        return this.cache.topEvents;
      }

      logger.log('Preloading top events in background...');
      const topEvents = await eventService.getTopEvents(limit);
      
      this.cache.topEvents = topEvents;
      this.cache.lastUpdated.events = Date.now();
      
      logger.log(`✅ Preloaded ${topEvents.length} top events`);
      return topEvents;
    } catch (error) {
      logger.error('Error preloading top events:', error);
      return this.cache.topEvents || []; // Return cached data or empty array
    }
  }

  /**
   * Preload top clubs in background
   */
  async preloadTopClubs(limit = 10) {
    try {
      if (this.isCacheValid('clubs') && this.cache.topClubs) {
        logger.log('Using cached top clubs');
        return this.cache.topClubs;
      }

      logger.log('Preloading top clubs in background...');
      const topClubs = await clubService.getTopClubs(limit);
      
      this.cache.topClubs = topClubs;
      this.cache.lastUpdated.clubs = Date.now();
      
      logger.log(`✅ Preloaded ${topClubs.length} top clubs`);
      return topClubs;
    } catch (error) {
      logger.error('Error preloading top clubs:', error);
      return this.cache.topClubs || []; // Return cached data or empty array
    }
  }

  /**
   * Preload both events and clubs in background
   */
  async preloadAll() {
    if (this.isPreloading) {
      logger.log('Preloading already in progress, skipping...');
      return;
    }

    try {
      this.isPreloading = true;
      logger.log('🚀 Starting background preloading...');

      // Preload both in parallel for better performance
      const [topEvents, topClubs] = await Promise.all([
        this.preloadTopEvents(10),
        this.preloadTopClubs(10)
      ]);

      logger.log(`✅ Background preloading completed: ${topEvents.length} events, ${topClubs.length} clubs`);
      
      return {
        events: topEvents,
        clubs: topClubs
      };
    } catch (error) {
      logger.error('Error in background preloading:', error);
    } finally {
      this.isPreloading = false;
    }
  }

  /**
   * Get cached top events (instant)
   */
  getCachedTopEvents() {
    return this.cache.topEvents || [];
  }

  /**
   * Get cached top clubs (instant)
   */
  getCachedTopClubs() {
    return this.cache.topClubs || [];
  }

  /**
   * Initialize preloading on app start
   */
  async initialize() {
    logger.log('🔄 Initializing preloading service...');
    
    // Start preloading in background (don't await)
    this.preloadAll().catch(error => {
      logger.error('Error during initialization preloading:', error);
    });

    // Set up periodic refresh every 5 minutes
    setInterval(() => {
      logger.log('🔄 Refreshing preloaded data...');
      this.preloadAll().catch(error => {
        logger.error('Error during periodic preloading:', error);
      });
    }, this.cacheTimeout);
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  clearCache() {
    this.cache = {
      topEvents: null,
      topClubs: null,
      lastUpdated: {
        events: null,
        clubs: null
      }
    };
    logger.log('🗑️ Preloading cache cleared');
  }

  /**
   * Get cache status for debugging
   */
  getCacheStatus() {
    return {
      events: {
        cached: !!this.cache.topEvents,
        count: this.cache.topEvents?.length || 0,
        lastUpdated: this.cache.lastUpdated.events,
        isValid: this.isCacheValid('events')
      },
      clubs: {
        cached: !!this.cache.topClubs,
        count: this.cache.topClubs?.length || 0,
        lastUpdated: this.cache.lastUpdated.clubs,
        isValid: this.isCacheValid('clubs')
      },
      isPreloading: this.isPreloading
    };
  }
}

// Create singleton instance
const preloadingService = new PreloadingService();

export default preloadingService;
